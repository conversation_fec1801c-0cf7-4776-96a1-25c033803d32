#!/usr/bin/env bash
# @since 2022-08-04 10:47:10
# <AUTHOR> <EMAIL>

set -euo pipefail

is_error=0

function echo_error() {
  echo -e "\033[31mERROR: $1\033[0m"
  is_error=1
}

function echo_info() {
  echo -e "\033[32mINFO: $1\033[0m"
}

function check_native_modules() {
  if grep -E $(echo $(grep -E "from '[^.']+'" ./src/utils/NativeModules.ts -o | awk -F "'" '{print $2}') | sed 's/ /|/g') -r ./src | grep -v src/utils/NativeModules.ts | grep -v .d.ts:; then
    echo_error 'please import native modules from src/utils/NativeModules.ts rather than node modules directly.'
  fi
}

function check_moment() {
  if grep -E 'from .moment.' -r src; then
    echo_error 'moment is deprecated, use dayjs instead.'
  fi
}

function check_ts_ignore() {
  echo_info "Checking ts-ignore"
  if git --no-pager grep -n -i @ts''-ignore; then
    echo_error '@ts''-ignore is forbidden, please fix it or use @ts-expect-error instead.'
  fi
}

function check_index() {
  echo_info "Checking index files"
  if git ls-files | grep -v -E '^(src|public)/index' | grep /index; then
    echo_error 'index.* is forbidden, please use valid file name instead.'
  fi
}

function check_default() {
  echo_info "Checking export default"
  if git grep 'export default' -- src | grep -v '.d.ts:'; then
    echo_error 'export default is forbidden, please use export { xxx } instead.'
  fi
}

function check_circular() {
  echo_info "Checking circular dependencies"
  if ! npx dpdm -T --no-tree --no-warning --exit-code circular:1 src/index.tsx; then
    echo_error 'Circular dependencies detected, please fix it.'
  fi
}

function check_prettier() {
  echo_info "Checking prettier"
  if ! npx prettier --check .; then
    echo_error 'Code is not formatted correctly, please fix it.'
  fi
}

function check_types() {
  echo_info "Checking types"
  if ! npx tsc --noEmit; then
    echo_error 'Ts check failed, please fix it.'
  fi
}

function check_commit_msg() {
  echo_info "Checking commit messages"
  if ! npx commitlint --from origin/main; then
    echo_error 'Commit message check failed, please fix it.'
  fi
}

if [[ $# -gt 0 ]]; then
  "check_$1"
else
  export CHECK_TS_TYPE="false"
  export XDG_DATA_HOME="$PWD/.cache"

#  pnpm install --frozen-lockfile

  check_circular
  check_prettier
  check_types
  check_commit_msg
  check_ts_ignore
  check_index
  check_default
fi

if [[ $is_error -eq 1 ]]; then
  exit 1
fi
