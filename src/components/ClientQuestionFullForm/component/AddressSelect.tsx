import { AddressAutoCompleteRef } from '@moego/client-lib-components/dist/Address/AddressAutoComplete';
import { formatAddress } from '@moego/client-lib-utils/dist/format';
import { InputController } from '@moego/client-lib-widgets/dist/Input/utils';
import { useSetAtom } from 'jotai';
import { AddressState, ContactInfoFormAddress } from 'pages/PersonalInfo/ContactInfo/ContactInfoFormAddress';
import { forwardRef, memo, useImperativeHandle, useRef, useState } from 'react';
import { useBoolean } from 'react-use';
import { bookingNewAddressState } from 'state/booking/state';
import { CustomerQuestionKey } from 'types/question';
import { useInput, validateInput } from 'widgets/Input/utils';
import { useClientQuestionConfig } from '../hooks/useQuestionConfig';

export interface AddressInfoRef {
  setReadonly: (v: boolean) => unknown;
  validate: () => Promise<AddressState | undefined>;
  isShow: boolean;
  isRequired: boolean;
  setValue: (v: AddressState) => void;
}

export interface AddressSelectProps {
  defaultValue?: AddressState;
}

export const AddressSelect = memo(
  forwardRef<AddressInfoRef, AddressSelectProps>((props, ref) => {
    const { defaultValue } = props;
    const addressRef = useRef<AddressAutoCompleteRef | null>(null);
    const addressInput = useInput(formatAddress(defaultValue || {}), {
      validator: (value: string) => {
        if (!isRequired) {
          return null;
        }
        if (!value.toString().length) {
          return 'This field is required';
        }
        if (!addressRef.current?.suggestedAddress) {
          return 'Please select an address from the search result.';
        }
        return null;
      },
    });
    const [isChanged, setIsChanged] = useBoolean(false);
    const [state, setState] = useState(defaultValue);
    const setBookingAddress = useSetAtom(bookingNewAddressState);

    const { isShow, isRequired } = useClientQuestionConfig(CustomerQuestionKey.Address);

    const validate = async () => {
      if (!isShow) return;
      // 这个组件内部无法 validate 预填的地址，但这个组件太多层的 ref 转发不好处理，所以直接在这种情况绕过
      if (!isChanged) return defaultValue;
      const value = (await validateInput(addressInput))?.trim();
      if (isRequired && !state) {
        throw new Error('Address is required');
      }
      return value ? state : undefined;
    };

    useImperativeHandle(ref, () => ({
      validate: validate,
      setReadonly: addressInput.setReadonly,
      isShow,
      isRequired,
      setValue: (v: AddressState) => {
        addressInput.setState({ value: formatAddress(v || {}) });
      },
    }));

    if (!isShow) {
      return null;
    }

    return (
      <ContactInfoFormAddress
        addressRef={addressRef}
        input={addressInput as InputController}
        readonly={false}
        required={isRequired}
        onChange={(newVal) => {
          setState(newVal?.address);
          setIsChanged(true);
          setBookingAddress((v) => ({ ...v, ...newVal }));
        }}
      />
    );
  }),
);
