import { default as PuzzledDog } from 'assets/images/puzzled-dog.png';
import { memo, useCallback, useEffect, useState } from 'react';
import { landingRoute } from 'routes';
import { postMessageToClientPortalApp } from 'utils/portal/portal';
import { Transition } from 'widgets/Transition/Transition';
import { OperationSection } from './OperationSection';

export interface ErrorSectionProps {
  title: string;
  description?: string;
  img?: string;
  allowBackHome?: boolean;
  show?: boolean;
}

function ErrorSectionComponent({
  title,
  description,
  img = PuzzledDog,
  show = true,
  allowBackHome,
}: ErrorSectionProps) {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (show) {
      const timer = setTimeout(() => setVisible(true), 0);
      return () => window.clearInterval(timer);
    } else {
      setVisible(false);
    }
  }, [show]);

  const onBackToHome = useCallback(() => {
    postMessageToClientPortalApp({
      type: 'back',
      data: {
        canGoBack: false,
        currentPath: window.location.pathname,
      },
    });
    landingRoute.replace().then(() => window.location.reload());
  }, []);

  return (
    <Transition name="notfound" show={visible}>
      <div className="h-full-compatible bg-base-100 flex-center flex-col text-center pb-[15vh]">
        <img src={img} alt="error" className="w-[224px] h-[224px] mb-[20px] bg-primary-light rounded-full" />
        <h1>{title}</h1>
        {description ? (
          <div className="px-[20px] text-[12px] text-text-secondary">
            {description
              .split('\n')
              .filter((i) => !!i)
              .map((line, index) => (
                <p key={index} className={index === 0 ? 'mb-[12px] text-[16px] text-[#333]' : undefined}>
                  {line}
                </p>
              ))}
          </div>
        ) : null}
      </div>
      {allowBackHome ? (
        <OperationSection className="bg-opacity-0 shadow-none web:mx-[var(--web-mx)]">
          <button
            className="btn btn-moe-large w-full bg-secondary-line text-base-content border-opacity-0 hover:bg-secondary-line hover:border-opacity-0"
            onClick={onBackToHome}
          >
            Back to home
          </button>
        </OperationSection>
      ) : null}
    </Transition>
  );
}

export const ErrorSection = memo(ErrorSectionComponent);
