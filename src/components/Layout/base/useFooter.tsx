import { useMergedConfig } from 'hooks/useMergedConfig';
import { cn } from 'utils/classNames';
import { Button } from 'widgets/Button/Button';
import { FooterButtonProps } from '../Layout';
import { RenderComponent } from './types';

export function useFooter(_footerButtonConfig?: FooterButtonProps, renderDefault?: RenderComponent) {
  const [enableCustom, footerButtonConfig] = useMergedConfig(_footerButtonConfig, {});

  const enableFooter = !!enableCustom || !!renderDefault;

  const defaultRender = () => {
    return (
      <Button
        {...footerButtonConfig}
        className={cn(
          'btn btn-moe-large btn-shadow btn-primary w-[calc(100vw-40px)] rounded-full',
          footerButtonConfig.className,
        )}
      >
        {footerButtonConfig.title}
      </Button>
    );
  };

  const mergedRenderFooterButton = renderDefault ?? defaultRender;

  return {
    footerButtonConfig,
    enableFooter,
    mergedRenderFooterButton,
  };
}
