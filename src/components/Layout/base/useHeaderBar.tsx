import { useMergedConfig } from 'hooks/useMergedConfig';
import { useState } from 'react';
import { HeaderBar, HeaderBarProps } from '../HeaderBar/HeaderBar';
import { HeaderBarLeft } from '../HeaderBar/HeaderBarLeft';
import { HeaderBarRight } from '../HeaderBar/HeaderBarRight';

export type RenderHeaderBar = (headerBarState: { sticky?: boolean }) => React.ReactNode;

export function useHeaderBar(_HeaderBarConfig?: HeaderBarProps, renderHeaderBar?: RenderHeaderBar) {
  const [enableCustom, headerBarConfig] = useMergedConfig(_HeaderBarConfig, {
    // just used to make the type of children correct
    children: [<HeaderBarLeft />, <HeaderBarRight />],
    offsetTop: 0,
  });

  const [headerBarSticky, setHeaderBarSticky] = useState(false);

  const enableHeaderBar = !!enableCustom || !!renderHeaderBar;

  const defaultRender = () => {
    return <HeaderBar {...headerBarConfig} sticky={headerBarSticky} />;
  };

  const mergedRenderHeaderBar = renderHeaderBar ? () => renderHeaderBar({ sticky: headerBarSticky }) : defaultRender;

  return {
    headerBarConfig,
    headerBarSticky,
    enableHeaderBar,
    setHeaderBarSticky,
    mergedRenderHeaderBar,
  };
}
