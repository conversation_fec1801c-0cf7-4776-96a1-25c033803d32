import { PageTitle, PageTitleProps } from '@moego/client-lib-components/dist/PageTitle/PageTitle';
import { useMergedConfig } from 'hooks/useMergedConfig';
import { useState } from 'react';
import { Icon, IconType } from 'widgets/Icon';
import { RenderComponent } from './types';

export function usePageTitle(_pageTitleConfig?: PageTitleProps, renderTitle?: RenderComponent) {
  const [enableCustom, pageTitleConfig] = useMergedConfig(_pageTitleConfig, {
    offsetTop: 0,
  });

  const [titleSticky, setTitleSticky] = useState(false);

  const enablePageTitle = !!enableCustom || !!renderTitle;

  const defaultRenderTitle = () => {
    return (
      <PageTitle
        {...pageTitleConfig}
        subTitle={undefined}
        sticky={titleSticky}
        icon={<Icon fontSize={24} name={IconType.arrowLeft} />}
      />
    );
  };

  const mergedRenderTitle = renderTitle ?? defaultRenderTitle;

  return {
    pageTitleConfig,
    titleSticky,
    enablePageTitle,
    setTitleSticky,
    mergedRenderTitle,
  };
}
