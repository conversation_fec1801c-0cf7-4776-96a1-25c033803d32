import { default as classNames } from 'classnames';
import { memo, ReactNode } from 'react';

export interface TitleSectionProps {
  className?: string;
  title?: ReactNode;
  subtitle?: ReactNode;
  titleClassName?: string;
  subtitleClassName?: string;
}

function TitleSectionComponent({ title, subtitle, className, titleClassName, subtitleClassName }: TitleSectionProps) {
  return (
    <div className={className}>
      <div className={classNames('text-ellipsis text-base-content', titleClassName)}>{title}</div>
      <div className={classNames('text-ellipsis text-base-content/60 text-xs', subtitleClassName)}>{subtitle}</div>
    </div>
  );
}

export const TitleSection = memo(TitleSectionComponent);
