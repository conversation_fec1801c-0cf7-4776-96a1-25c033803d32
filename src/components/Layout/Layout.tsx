import { PageTitleProps } from '@moego/client-lib-components/dist/PageTitle/PageTitle';
import { useObserverWidth } from '@moego/client-lib-components/dist/utils/useObserverWidth';
import { usePortalAppConfig } from 'components/Portal/PortalAppHelperWrapper';
import { throttle } from 'lodash';
import React, { forwardRef, memo, useMemo, useRef } from 'react';
import { cn, GenerateClassNames, generateSlots } from 'utils/classNames';
import { RenderComponent } from './base/types';
import { useFooter } from './base/useFooter';
import { RenderHeaderBar, useHeaderBar } from './base/useHeaderBar';
import { usePageTitle } from './base/usePageTitle';
import { HeaderBarProps } from './HeaderBar/HeaderBar';

const slots = generateSlots([
  'layoutBase',
  'layoutHeader',
  'layoutHeaderHeaderBar',
  'layoutHeaderPageTitle',
  'layoutPageSubTitle',
  'layoutContent',
  'layoutFooter',
]);

export interface FooterButtonProps {
  className?: string;
  onClick: () => void;
  title: string;
  disabled?: boolean;
}

export interface LayoutProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'title'> {
  renderHeaderBar?: RenderHeaderBar;
  renderTitle?: RenderComponent;
  renderFooter?: RenderComponent;
  /**
   * Use for default pageTitle component.
   */
  pageTitleConfig?: PageTitleProps;
  /**
   * Use for custom header bar component.
   */
  headerBarConfig?: HeaderBarProps;
  classNames?: GenerateClassNames<typeof slots>;
  /**
   * Triggered when the footer button in the header bar is clicked.
   */
  footerButtonConfig?: FooterButtonProps;
}

const CONTENT_PADDING_BOTTOM = 24;

export const Layout = memo(
  forwardRef<HTMLDivElement, LayoutProps>((props, forwardedRef) => {
    const {
      className,
      children,
      pageTitleConfig: _pageTitleConfig,
      headerBarConfig: _headerBarConfig,
      footerButtonConfig: _footerButtonConfig,
      classNames = {},
      renderHeaderBar,
      renderTitle,
      renderFooter,
      onScroll,
    } = props;

    const { enablePageTitle, pageTitleConfig, setTitleSticky, mergedRenderTitle } = usePageTitle(
      _pageTitleConfig,
      renderTitle,
    );
    const { headerBarConfig, enableHeaderBar, setHeaderBarSticky, mergedRenderHeaderBar } = useHeaderBar(
      _headerBarConfig,
      renderHeaderBar,
    );
    const { enableFooter, mergedRenderFooterButton } = useFooter(_footerButtonConfig, renderFooter);
    const { appTop } = usePortalAppConfig();

    const scrollerRef = useRef<HTMLDivElement>(null);
    const pageTitleRef = useRef<HTMLDivElement>(null);
    const headerBarRef = useRef<HTMLDivElement>(null);
    const footerRef = useRef<HTMLDivElement>(null);

    const resizeWidth = useObserverWidth(scrollerRef);

    const contentStyle = useMemo(() => {
      return {
        height: `calc(var(--app-height) - ${
          (pageTitleRef.current?.offsetHeight || 0) + (headerBarRef.current?.offsetHeight || 0)
        }px)`,
        ...(footerRef.current
          ? {
              paddingBottom: `${
                footerRef.current.offsetHeight +
                  parseInt(getComputedStyle(footerRef.current).bottom) +
                  CONTENT_PADDING_BOTTOM || 0
              }px`,
            }
          : {}),
      };
    }, [resizeWidth, headerBarRef.current?.offsetHeight]);

    const handleScroll = throttle((event) => {
      if (scrollerRef.current) {
        enablePageTitle && setTitleSticky(scrollerRef.current.scrollTop > pageTitleConfig.offsetTop);
        enableHeaderBar && setHeaderBarSticky(scrollerRef.current.scrollTop > headerBarConfig.offsetTop);
      }
      onScroll?.(event);
    }, 50);

    return (
      <>
        <div
          {...slots.layoutBase}
          ref={forwardedRef}
          className={cn('bg-white', className, classNames.layoutBase)}
          {...(appTop
            ? {
                style: {
                  paddingTop: appTop,
                },
              }
            : {})}
        >
          {/* HeaderBar */}
          <div data-slot="layout-header" className={cn(classNames.layoutHeader, 'empty:hidden')}>
            {enableHeaderBar && (
              <div
                {...slots.layoutHeaderHeaderBar}
                ref={headerBarRef}
                className={cn('transition-child', classNames.layoutHeaderHeaderBar)}
              >
                {mergedRenderHeaderBar()}
              </div>
            )}
            {enablePageTitle && (
              <div
                {...slots.layoutHeaderPageTitle}
                ref={pageTitleRef}
                className={cn('transition-child', classNames.layoutHeaderPageTitle)}
              >
                {mergedRenderTitle()}
              </div>
            )}
          </div>

          {/* LayoutContent */}
          <div
            ref={scrollerRef}
            {...slots.layoutContent}
            className={cn(
              'px-[16px] w-full pb-[24px]',
              'overflow-x-hidden overflow-y-auto',
              'web:px-[calc((100vw-var(--web-width))/2)]',
              classNames.layoutContent,
            )}
            id="layout-scroller-container"
            style={contentStyle}
            onScroll={handleScroll}
          >
            {/* SubTitle */}
            {pageTitleConfig.subTitle ? (
              <div
                {...slots.layoutPageSubTitle}
                className={cn('text-mobile-subhead text-text-secondary mb-[24px]', classNames.layoutPageSubTitle)}
              >
                {pageTitleConfig.subTitle}
              </div>
            ) : null}
            {/* Content */}
            {children}
          </div>

          {/* Footer */}
          {enableFooter ? (
            <div
              ref={footerRef}
              {...slots.layoutFooter}
              className={cn(
                'absolute left-1/2 -translate-x-1/2 bottom-[24px] web:bottom-[100px]',
                classNames.layoutFooter,
              )}
            >
              {mergedRenderFooterButton()}
            </div>
          ) : null}
        </div>
      </>
    );
  }),
);

Layout.displayName = 'Layout';
