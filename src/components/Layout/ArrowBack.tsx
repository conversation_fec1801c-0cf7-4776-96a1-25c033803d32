import { default as classNames } from 'classnames';
import { useScreen } from 'hooks/useScreen';
import { memo } from 'react';
import { Icon, IconType } from 'widgets/Icon';

interface ArrowBackProps {
  backClassName?: string;
  onBack?: () => unknown;
}

const ArrowBackComponent = ({ backClassName, onBack }: ArrowBackProps) => {
  const { isWeb } = useScreen();

  const IconArrowLeft = (
    <Icon name={IconType.arrowLeft} className={classNames('w-[24px] h-[24px] web:fixed', backClassName)} />
  );

  return isWeb ? (
    <div
      className="fixed top-[28px] left-[20px] cursor-pointer flex justify-center items-center w-[48px] h-[48px] bg-white rounded-[50%] border-[1px] border-solid lg:left-[70px]"
      onClick={onBack || back}
    >
      {IconArrowLeft}
    </div>
  ) : (
    <div className="cursor-pointer" onClick={onBack || back}>
      {IconArrowLeft}
    </div>
  );
};

function back() {
  window.history.back();
}

export const ArrowBack = memo(ArrowBackComponent);
