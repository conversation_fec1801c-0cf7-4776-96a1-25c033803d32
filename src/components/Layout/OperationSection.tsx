import { useLazyEntryNode } from '@moego/client-lib-hooks/dist/useEntryNode';
import { useThemeColorMeta } from '@moego/client-lib-hooks/dist/useThemeColor';
import { default as classNames } from 'classnames';
import { ForwardedRef, forwardRef, memo, PropsWithChildren, ReactNode } from 'react';
import { createPortal } from 'react-dom';
import { cn } from 'utils/classNames';
import { Icon, IconType } from 'widgets/Icon';
import { Transition, TransitionProps, TransitionText } from 'widgets/Transition/Transition';

export interface OperationSectionProps extends Omit<TransitionProps, 'name'> {
  portal?: boolean;
  className?: string;
  containerStyle?: React.CSSProperties;
  mask?: boolean;
  maskStyle?: React.CSSProperties;
  maskClassName?: string;
  clickMaskToClose?: boolean;
  needShadow?: boolean;
  title?: string;
  titleClassName?: string;
  transitionTitle?: boolean;
  renderHeader?: () => ReactNode;
  headerSticky?: boolean;
  goBack?: () => void;
  renderBeforeTitle?: () => ReactNode;
  onClose?: () => unknown;
  headerClassName?: string;
}

function wrapper({ condition, child, className }: { condition: boolean; child: ReactNode; className?: string }) {
  return condition ? <div className={className}>{child}</div> : child;
}

function OperationSectionComponent(
  {
    show = true,
    portal,
    className,
    containerStyle,
    transitionDelay = 800,
    children,
    onClose,
    mask,
    maskStyle,
    maskClassName,
    clickMaskToClose = true,
    transition,
    needShadow = false,
    title,
    renderHeader,
    titleClassName,
    transitionTitle,
    headerSticky,
    goBack,
    renderBeforeTitle,
    headerClassName,
    ...rest
  }: PropsWithChildren<OperationSectionProps>,
  ref: ForwardedRef<HTMLDivElement>,
) {
  const entry = useLazyEntryNode();
  const titleClassNames = title
    ? classNames(
        'h-[24px] text-[18px] font-bold',
        headerSticky ? '' : 'absolute top-[24px] left-[20px] web:top-[32px] web:left-[32px]',
        titleClassName,
      )
    : null;

  if (mask) {
    useThemeColorMeta(show ? '#7F7F7F' : '#fff');
  }

  const previousArrow = goBack ? (
    <Icon name={IconType.left} className="w-[28px] h-[28px] mt-[-1px] mr-[10px] ml-[-10px]" onClick={goBack} />
  ) : null;

  const content = (
    <>
      {show && mask ? (
        <div
          className={cn(
            'h-full-compatible fixed top-0 left-0 w-full bg-[rgba(0,0,0,0.5)] appear-opacity !m-0',
            maskClassName,
          )}
          style={maskStyle}
          onClick={clickMaskToClose ? onClose : undefined}
        />
      ) : null}
      <Transition
        {...rest}
        name="operation-section"
        show={show}
        transitionDelay={transitionDelay}
        transition={transition}
        asFragment
      >
        <div
          className={cn(
            // Note: 使用时候注意，添加类名 web:mx-auto / web:mx-[var(--web-mx)] 取决于 <OperationSection/> 是否放在 <MainSection/> 里
            // Note 2: 这里应有一个 overflow-x-hidden，但一下改影响面大，先各自加，后续统一
            'operation-section fixed bottom-[env(safe-area-inset-bottom,0)] w-full max-w-[var(--web-width)] rounded-t-2xl bg-base-100 px-[20px] pt-[24px] pb-[24px] web:text-center web:px-[32px] web:py-[32px] web:bottom-[32px] web:rounded-b-2xl',
            needShadow ? 'shadow-[0_-8px_20px_rgba(0,0,0,0.06)]' : '',
            headerSticky ? 'pt-0' : '',
            className,
          )}
          style={containerStyle}
          ref={ref}
        >
          {wrapper({
            condition: !!headerSticky,
            child: (
              <>
                {renderHeader ? (
                  renderHeader?.()
                ) : title ? (
                  transitionTitle ? (
                    <div className={classNames(titleClassNames, 'flex items-center')}>
                      {previousArrow}
                      {renderBeforeTitle?.()}
                      <TransitionText>{title}</TransitionText>
                    </div>
                  ) : (
                    <p className={classNames(titleClassNames, 'flex items-center')}>
                      {previousArrow}
                      {renderBeforeTitle?.()}
                      {title}
                    </p>
                  )
                ) : null}
                {onClose ? (
                  <Icon
                    name={IconType.close}
                    className={classNames(
                      'absolute top-[24px] right-[20px] h-[24px] w-[24px] web:top-[32px] web:right-[32px] cursor-pointer',
                      headerSticky ? '!top-0 !right-0' : '',
                    )}
                    onClick={onClose}
                  />
                ) : null}
              </>
            ),
            className: classNames(
              'relative sticky top-0 bg-white z-[1] border-t-[24px] border-b-[20px] border-solid border-white web:border-t-[32px]',
              headerClassName,
            ),
          })}
          {children}
        </div>
      </Transition>
    </>
  );

  return portal ? createPortal(content, entry()) : content;
}

export const OperationSection = memo(forwardRef(OperationSectionComponent));
