import classNames from 'classnames';
import React, { CSSProperties, forwardRef, HTMLAttributes, memo, ReactElement } from 'react';
import { Condition } from 'widgets/Condition';
import { Icon, IconType } from 'widgets/Icon';

export interface HeaderBarProps extends Omit<HTMLAttributes<HTMLDivElement>, 'children'> {
  /**
   * The children of the header bar.
   * @type HeaderBarLeft / HeaderBarRight
   */
  children?: [ReactElement, ReactElement];
  /**
   * The left side of the header bar.
   */
  headerBarLeft?: ReactElement;
  /**
   * The right side of the header bar.
   */
  headerBarRight?: ReactElement;
  /**
   * Whether the header bar should be sticky.
   * @default true
   */
  sticky?: boolean;
  /**
   * The offsetTop of the header bar when sticky.
   * @default 0
   */
  offsetTop?: number;
  /**
   * The z-index of the header bar when sticky.
   * @default 999
   */
  zIndex?: number;
  /**
   * Whether to show the divider on the right side of the header bar.
   * @default true
   */
  showDivider?: boolean;
  leftClassName?: string;
  rightClassName?: string;
}

export const HeaderBar = memo(
  forwardRef<HTMLDivElement, HeaderBarProps>((props, forwardedRef) => {
    const {
      children,
      className,
      leftClassName,
      rightClassName,
      headerBarLeft,
      headerBarRight,
      showDivider = true,
      sticky = true,
      offsetTop = 0,
      zIndex = 999,
      ...restProps
    } = props;

    const offsetStyle: CSSProperties = sticky ? { position: 'sticky', top: offsetTop, zIndex } : {};

    const [HeaderBarLeft, HeaderBarRight] = React.Children.toArray(children);

    const mergedHeaderBarLeft = headerBarLeft ?? HeaderBarLeft;
    const mergedHeaderBarRight = headerBarRight ?? HeaderBarRight;

    if (!mergedHeaderBarLeft && !mergedHeaderBarRight) {
      return null;
    }

    return (
      <div
        {...restProps}
        ref={forwardedRef}
        data-slot="header-bar"
        data-sticky={sticky}
        className={classNames(
          'flex items-center w-full justify-between px-[20px] py-[8px] gap-[8px]',
          'text-mobile-subhead text-[#202020]',
          'bg-white',
          'border-b-[1px] border-solid border-[#E6E6E6]',
          'web:px-[calc((100vw-var(--web-width))/2)] web:justify-start',
          {
            'web:shadow-[0px_4px_4px_0px_rgba(218,_218,_218,_0.25)] web:border-none': !sticky,
          },
          className,
        )}
        style={{
          ...restProps.style,
          ...offsetStyle,
        }}
      >
        <Condition if={mergedHeaderBarLeft}>
          <div
            data-slot="header-bar-left-base"
            className={classNames('flex items-center gap-[4px]', 'web:w-[50%]', leftClassName)}
          >
            {mergedHeaderBarLeft}
          </div>
        </Condition>
        <Condition if={mergedHeaderBarRight}>
          <div data-slot="header-bar-right-base" className={classNames('flex items-center gap-[8px]', rightClassName)}>
            <Condition if={showDivider}>
              <Icon className="w-[2px] h-[24px] flex-shrink-0 text-[#E6E6E6]" name={IconType.divider}></Icon>
            </Condition>
            {mergedHeaderBarRight}
          </div>
        </Condition>
      </div>
    );
  }),
);
