import classNames from 'classnames';
import { forwardRef, HTMLAttributes, memo, ReactNode } from 'react';
import { Icon, IconType } from 'widgets/Icon';

interface HeaderBarRightProps extends HTMLAttributes<HTMLDivElement> {
  children?: ReactNode;
  /**
   * The icon to display on the right side of the header bar.
   * @default 'pet-dog-avatar'
   */
  icon?: ReactNode;
  /**
   * The font size of the default icon.
   * @default 24
   */
  fontSize?: number;
  /**
   * The name of the default icon.
   * @default 'pet-dog-avatar'
   */
  name?: IconType;
}

export const HeaderBarRight = memo(
  forwardRef<HTMLDivElement, HeaderBarRightProps>((props, forwardedRef) => {
    const { children, className, icon: _icon, name: _name, fontSize = 24, ...restProps } = props;

    const name = _name ?? IconType.petDogAvatar;
    const icon = _icon ?? <Icon fontSize={fontSize} name={name} />;

    return (
      <div
        {...restProps}
        ref={forwardedRef}
        data-slot="header-bar-right"
        className={classNames('flex items-center gap-[4px]', className)}
      >
        <div className="bg-[#F3F3F3] rounded-full">{icon}</div>
        {children}
      </div>
    );
  }),
);
