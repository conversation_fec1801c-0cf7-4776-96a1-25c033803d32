import classNames from 'classnames';
import { forwardRef, HTMLAttributes, memo, ReactNode } from 'react';
import { Icon, IconType } from 'widgets/Icon';

interface HeaderBarLeftProps extends HTMLAttributes<HTMLDivElement> {
  children?: ReactNode;
  /**
   * The icon to display on the left side of the header bar, usually, you don't need to manually modify it.
   * @default 'major-daycare-outlined'
   */
  icon?: ReactNode;
  /**
   * The font size of the default icon.
   * @default 24
   */
  fontSize?: number;
  /**
   * The name of the default icon.
   * @default 'major-daycare-outlined'
   */
  name?: IconType;
}

export const HeaderBarLeft = memo(
  forwardRef<HTMLDivElement, HeaderBarLeftProps>((props, forwardedRef) => {
    const { children, className, icon: _icon, name: _name, fontSize = 24, ...restProps } = props;

    const name = _name ?? IconType.majorDaycareOutlined;
    const icon = _icon ?? <Icon fontSize={fontSize} name={name} />;

    return (
      <div
        {...restProps}
        ref={forwardedRef}
        data-slot="header-bar-left"
        className={classNames('flex items-center gap-[4px]', className)}
      >
        {icon}
        {children}
      </div>
    );
  }),
);
