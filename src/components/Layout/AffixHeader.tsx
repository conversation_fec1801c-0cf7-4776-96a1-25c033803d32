import { default as throttle } from 'lodash/throttle';
import {
  CSSProperties,
  ForwardedRef,
  forwardRef,
  memo,
  MutableRefObject,
  PropsWithChildren,
  useEffect,
  useRef,
  useState,
} from 'react';

interface AffixHeaderProps {
  title: string;
  style?: CSSProperties;
}

const endY = 68;
const endScale = 0.529;
const dScale = 1 - endScale;
const dY = 102;

function AffixHeaderComponent(
  { title, style, children }: PropsWithChildren<AffixHeaderProps>,
  _ref: ForwardedRef<HTMLDivElement>,
) {
  const titleRef = useRef<HTMLHeadingElement>(null);
  const [percentage, setPercentage] = useState(0);
  const [[titleWidth, contentWidth], setWidth] = useState([0, 0]);

  const scale = endScale + (1 - percentage) * dScale;
  const translateY = dY * percentage;
  const translateX = (contentWidth - titleWidth) * percentage;

  const ref = _ref as MutableRefObject<HTMLDivElement>;
  useEffect(() => {
    if (!ref.current) {
      return;
    }
    const set = throttle(
      () => {
        if (!ref.current) {
          return;
        }
        if (ref.current.scrollTop > endY) {
          setPercentage(1);
          return;
        }
        const p = ref.current.scrollTop / (endY - 20);
        setPercentage(Math.round(p));
      },
      20,
      { leading: true, trailing: true },
    );
    ref.current.addEventListener('scroll', set);
    return () => ref.current?.removeEventListener('scroll', set);
  }, [ref.current]);

  useEffect(() => {
    if (titleRef.current) {
      setWidth([titleRef.current.offsetWidth, Math.min(window.innerWidth, 480) - 40]);
    }
  }, [titleRef.current]);

  return (
    <>
      <div
        className="sticky top-0 pt-[32px] z-[99] bg-base-100 pb-[20px] px-[20px] transition web:bg-transparent"
        style={{
          boxShadow: `0 8px 8px -8px rgba(0,0,0,${0.1 * percentage})`,
          ...style,
        }}
      >
        {children}
        <h1
          key={title} // Force rerender after title change to make width update
          ref={titleRef}
          className="text-title absolute mt-[20px] transition origin-center truncate web:left-[90px] web:mt-0 md:!left-0"
          style={{ transform: `scale(${scale}) translateY(-${translateY}px) translateX(${translateX}px)` }}
        >
          {title}
        </h1>
      </div>
      <div className="mt-[41px] p-[0.05px] w-[1px] h-[1px]" />
    </>
  );
}

export const AffixHeader = memo(forwardRef(AffixHeaderComponent));
