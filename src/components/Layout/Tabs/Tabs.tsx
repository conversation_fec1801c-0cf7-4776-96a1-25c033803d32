import { Tab, TabGroupProps } from '@headlessui/react';
import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import { ElementType, memo } from 'react';
import { cn } from 'utils/classNames';

enum TabsMode {
  Normal = 'normal',
  Full = 'full',
}

interface TabItem<T> {
  value: T;
  label: string;
}

interface TabsProps<T> extends Omit<TabGroupProps<ElementType>, 'onChange'> {
  tabs: TabItem<T>[];
  mode?: `${TabsMode}`;
  onChange?: (value: T, index: number, tab: TabItem<T>) => void | Promise<void>;
}

function TabsComponent<T extends string | number>(props: TabsProps<T>) {
  const { tabs, onChange, mode = TabsMode.Normal, ...rest } = props;
  const isFullMode = mode === TabsMode.Full;
  const handleChange = useLatestCallback((index) => {
    const targetTab = tabs[index];
    onChange?.(targetTab.value, index, targetTab);
  });
  return (
    <Tab.Group {...rest} onChange={handleChange}>
      <Tab.List className={cn('flex gap-[24px]', isFullMode && 'w-full')}>
        {tabs.map((tab) => (
          <Tab
            key={tab.value}
            className={({ selected }) =>
              cn(
                'py-4 focus:outline-none',
                'cursor-pointer whitespace-nowrap',
                'text-base relative pb-[4px]',
                'transition-colors duration-200 ease-in-out',
                selected ? 'text-primary font-normal' : 'text-gray-500 font-normal hover:text-gray-700',
                {
                  'flex-1 text-center': isFullMode,
                },
              )
            }
          >
            {({ selected }) => (
              <div className={cn('relative text-[16px]', selected ? 'font-bold text-primary' : 'text-[#828282]')}>
                {tab.label}
                <div
                  className={cn(
                    'absolute top-[28px] left-0 w-full h-[2px]',
                    'transform transition-all duration-200 ease-in-out',
                    selected ? 'bg-primary scale-x-100 opacity-100' : 'scale-x-0 opacity-0',
                  )}
                />
              </div>
            )}
          </Tab>
        ))}
      </Tab.List>
    </Tab.Group>
  );
}

export const Tabs = memo(TabsComponent) as typeof TabsComponent;
