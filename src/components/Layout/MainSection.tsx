import { default as classNames } from 'classnames';
import { CSSProperties, ForwardedRef, forwardRef, memo, MutableRefObject, ReactNode, useRef } from 'react';
import { AffixHeader } from './AffixHeader';
import { ArrowBack } from './ArrowBack';

export interface MainSectionProps {
  allowBack?: boolean;
  onBack?: () => unknown;
  backClassName?: string;
  title: string;
  header?: ReactNode | ReactNode[];
  children?: ReactNode | ReactNode[];
  className?: string;
  contentClassName?: string;
  affixHeader?: boolean;
  affixHeaderStyle?: CSSProperties;
}

function MainSectionComponent(
  {
    allowBack,
    affixHeader,
    affixHeaderStyle,
    onBack,
    title,
    header,
    className,
    backClassName,
    contentClassName,
    children,
  }: MainSectionProps & { ref?: MutableRefObject<HTMLDivElement> },
  ref: ForwardedRef<HTMLDivElement>,
) {
  const defaultRef = useRef<HTMLDivElement>(null);
  const innerRef = ref || defaultRef;

  return (
    <div
      ref={innerRef}
      className={classNames(
        'px-0 child:px-[20px] !scrollbar-hide w-[100vw]',
        'web:px-[calc((100vw-var(--web-width))/2)] web:bg-[#F5F2F0]',
        affixHeader
          ? 'h-full-compatible overflow-x-hidden overflow-y-scroll pb-[calc(env(safe-area-inset-bottom,0)+112px)]'
          : 'pt-[32px] web:pt-[32px]',
        className,
      )}
      data-component-name="MainSection"
      data-input-role="root"
    >
      {affixHeader ? (
        <AffixHeader ref={innerRef} title={title} style={affixHeaderStyle}>
          <ArrowBack backClassName={backClassName} onBack={onBack} />
        </AffixHeader>
      ) : (
        <>
          {allowBack || onBack ? <ArrowBack backClassName={backClassName} onBack={onBack} /> : null}
          {header}
          {title ? <h1 className="mt-[20px] web:ml-[90px] md:ml-0 web:mt-0 web:px-0">{title}</h1> : null}
        </>
      )}
      <div
        className={classNames(
          'px-0 child:px-[20px] web:bg-white web:rounded-[24px] web:w-[var(--web-vw-full)] web:fixed web:top-[105px] web:bottom-[32px] web:overflow-y-overlay web:overflow-x-hidden web:child:px-[32px]',
          contentClassName,
        )}
      >
        {children}
      </div>
    </div>
  );
}

export const MainSection = memo(forwardRef(MainSectionComponent));
