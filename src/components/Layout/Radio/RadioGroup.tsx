import React, { forwardRef, memo, useEffect } from 'react';
import { Condition } from 'widgets/Condition';
import { Radio } from './Radio';
import { RadioGroupContext } from './RadioContext';

export type RadioValue = string | number | boolean;

interface RadioGroupOptions {
  label: string;
  value: string;
  disabled?: boolean;
}

export interface RadioGroupProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange' | 'defaultValue'> {
  defaultValue?: RadioValue;
  options?: RadioGroupOptions[];
  value?: RadioValue;
  disabled?: boolean;
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
  onClick?: (e: React.MouseEvent) => void;
  errorMessage?: string;
}

export const RadioGroup = memo(
  forwardRef<HTMLDivElement, RadioGroupProps>((props, forwardedRef) => {
    const {
      className,
      options,
      disabled,
      children,
      defaultValue,
      value,
      onChange,
      onClick: propsOnClick,
      errorMessage,
    } = props;
    const [radioGroupValue, setRadioGroupValue] = React.useState<RadioValue | undefined>(value ?? defaultValue);

    const onClick = (e: React.MouseEvent) => {
      const val = (e.target as HTMLInputElement).value;

      // 未传值时，内部控制
      if (!('value' in props)) {
        setRadioGroupValue(val);
      }
      propsOnClick?.(e);
    };

    useEffect(() => {
      if ('value' in props) {
        setRadioGroupValue(value);
      }
    }, [value]);

    const contextValue = {
      onChange,
      onClick,
      value: radioGroupValue,
      disabled,
    };

    return (
      <RadioGroupContext.Provider value={contextValue}>
        <div ref={forwardedRef} className={className}>
          {options
            ? options.map((option) => {
                return (
                  <Radio key={option.label} id={String(option.value)} value={option.value} disabled={disabled}>
                    {option.label}
                  </Radio>
                );
              })
            : children}
          <Condition if={errorMessage}>
            <div className="text-error text-[14px] font-[500]">{errorMessage}</div>
          </Condition>
        </div>
      </RadioGroupContext.Provider>
    );
  }),
);

RadioGroup.displayName = 'RadioGroup';
