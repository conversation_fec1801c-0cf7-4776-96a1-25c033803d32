import React, { forwardRef, memo, useMemo } from 'react';
import { cn } from 'utils/classNames';
import { v4 as uuidV4 } from 'uuid';
import { Condition } from 'widgets/Condition';
import { RadioGroupContext } from './RadioContext';
import { RadioValue } from './RadioGroup';

export interface RadioProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onClick' | 'onChange'> {
  checked?: boolean;
  id?: string;
  value?: RadioValue;
  disabled?: boolean;
  footer?: React.ReactNode;
  footerClassName?: string;
  labelClassName?: string;
  onClick?: (e: React.MouseEvent) => void;
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
}

export const Radio = memo(
  forwardRef<HTMLDivElement, RadioProps>((props, forwardedRef) => {
    const { className, checked, children, value, disabled, footer, footerClassName, labelClassName } = props;
    const { onChange, onClick, value: contextValue, disabled: contextDisabled } = React.useContext(RadioGroupContext);
    const inputId = useMemo(() => uuidV4(), []);
    return (
      <>
        <div
          ref={forwardedRef}
          data-slot="radio-base"
          className={cn('flex item-center gap-[8px]', className)}
          onClick={onClick}
        >
          <input
            data-slot="radio-input"
            className={cn(
              'radio',
              'w-[20px] h-[20px]',
              'checked:bg-white checked:shadow-[0_0_0_6px_rgba(var(--color-primary))_inset] checked:border-primary',
              'bg-white',
            )}
            type="radio"
            id={inputId}
            disabled={disabled ?? contextDisabled}
            checked={checked ?? contextValue === value}
            value={String(value)}
            onChange={onChange}
          />
          <label className={labelClassName} htmlFor={inputId}>
            {children}
          </label>
        </div>
        <Condition if={footer}>
          <div
            className={cn(
              'flex flex-col gap-[4px] ml-[28px] text-mobile-subhead text-text-secondary ',
              footerClassName,
            )}
          >
            {footer}
          </div>
        </Condition>
      </>
    );
  }),
);

Radio.displayName = 'Radio';
