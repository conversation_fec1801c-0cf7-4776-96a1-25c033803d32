import { useTokenFromQuery } from 'hooks/useTokenFromQuery';
import { useAtomValue, useSetAtom } from 'jotai';
import { memo, PropsWithChildren, useEffect, useState } from 'react';
import { clientPortalWelcomeRoute } from 'routes';
import { isFromClientPortalState } from 'state/account/state';
import { businessInfoState } from 'state/business/state';
import { Condition } from 'widgets/Condition';

/**
 * 在路由参数存在 token 的情况下，首先用 token 进行登录，然后逻辑继续
 */
export const PortalAuthWrapper = memo((props: PropsWithChildren) => {
  const { children } = props;
  const { token: tokenFromQuery } = clientPortalWelcomeRoute.useQuery();
  const setIsFromClientPortal = useSetAtom(isFromClientPortalState);
  const [error, setError] = useState<Error>();

  const tokenReady = useTokenFromQuery(setError);

  if (error) {
    throw error;
  }

  useEffect(() => {
    if (!tokenReady || !tokenFromQuery) {
      return;
    }
    setIsFromClientPortal(true);
    clientPortalWelcomeRoute.discardQueries('token');
  }, [tokenReady, tokenFromQuery]);

  const { loading } = useAtomValue(businessInfoState);

  // 由于不管 token 存不存在，都会 tokenReady，所以需要作为参数判断确保鉴权时序
  return <Condition if={tokenReady && !loading}>{children}</Condition>;
});
