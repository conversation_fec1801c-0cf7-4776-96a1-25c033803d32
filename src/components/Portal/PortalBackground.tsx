import { isFromClientPortalApp } from 'utils/portal/portal';
import { usePortalAppConfig } from './PortalAppHelperWrapper';

export const PortalBackground = () => {
  const { themeColor, subThemeColor } = usePortalAppConfig();

  if (!isFromClientPortalApp || !themeColor) {
    return null;
  }

  return (
    <div className="absolute left-0 top-0 w-full pointer-events-none  text-[var(--color-primary)]">
      <svg
        className="absolute right-0 top-[34px]"
        width="73"
        height="125"
        viewBox="0 0 73 125"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          opacity="0.1"
          d="M120.77 64.3124C129.682 1.48337 56.9436 -24.1338 26.6876 31.7914C-8.51178 46.0164 -8.10472 92.3738 28.7994 105.694C34.4252 107.724 39.2032 108.664 43.6993 109.549C50.5866 110.904 56.8126 112.13 64.4106 116.942C102.637 141.151 139.068 101.857 120.77 64.3124Z"
          fill={themeColor}
        />
      </svg>
      <svg
        className="absolute left-0 top-[200px]"
        width="57"
        height="173"
        viewBox="0 0 57 173"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          opacity="0.2"
          d="M33.3301 138.394C9.18463 177.995 -41.0733 191.433 -78.9241 168.407C-116.775 145.382 -127.885 94.6125 -103.74 55.0111C-79.5945 15.4096 -5.63512 -14.887 32.2157 8.13857C70.0666 31.1641 57.4756 98.7926 33.3301 138.394Z"
          fill={subThemeColor || themeColor}
        />
      </svg>
    </div>
  );
};
