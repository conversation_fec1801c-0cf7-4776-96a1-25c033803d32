import { memo, PropsWithChildren, ReactNode } from 'react';

export interface DecoratorWithHeaderProps {
  showDecorator: boolean;
  label?: ReactNode;
}

export const DecoratorWithHeader = memo<PropsWithChildren<DecoratorWithHeaderProps>>(
  ({ showDecorator, label = '', children = null }) => {
    return showDecorator ? (
      <div>
        <div className="px-[16px] py-[8px] rounded-t-[20px] bg-[#EBF8FF] text-[#0089FF] text-caption">{label}</div>
        {children}
      </div>
    ) : (
      <>{children}</>
    );
  },
);
