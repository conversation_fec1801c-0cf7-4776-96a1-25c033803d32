import React, { memo } from 'react';
import { Slot } from './ItemCard';

export interface CardSlotProps extends React.HTMLAttributes<HTMLDivElement> {
  slot?: Slot;
}

export const CardSlot = memo<CardSlotProps>((props) => {
  const { children, slot } = props;

  const childrenList = React.Children.toArray(children);

  return (
    <>
      {childrenList.map((child, index) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child as React.ReactElement, {
            ...child.props,
            slot: slot,
            key: `${slot}-${index}`,
          });
        }

        return child;
      })}
    </>
  );
});

CardSlot.displayName = 'CardSlot';
