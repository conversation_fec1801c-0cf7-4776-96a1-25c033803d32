import { useResizeObserver } from 'hooks/useResizeObserver';
import React, { forwardRef, memo, useEffect, useRef, useState } from 'react';
import { cn, GenerateClassNames, generateSlots, GenerateSlotsKeys } from 'utils/classNames';
import { Condition } from 'widgets/Condition';
import { CardSlotProps } from './CardSlot';
import { DecoratorWithHeader } from './DecoratorWithHeader';

const slots = generateSlots([
  'picture',
  'header',
  'content',
  'headerContentWrapper',
  'footer',
  'contentHeader',
  'contentDescription',
  'contentFooter',
  'badge',
]);

export type Slot = GenerateSlotsKeys<typeof slots>;

export interface ItemCardRef {
  updateFooterHeight: () => void;
}

export interface ItemCardProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onClick'> {
  slot?: Slot;
  classNames?: GenerateClassNames<typeof slots>;
  /**
   * If true, the card will have rounded border.
   * @default false
   */
  borderCard?: boolean;
  /**
   * The header label. like "Requires meet and greet"
   */
  headerLabel?: string;
  /**
   * If true, the footer will be collapsed.
   * @default false
   */
  footerCollapse?: boolean;
  /**
   * If true, the card footerCollapse will be controlled.
   * @default false
   */
  collapsedValue?: boolean;
  disabled?: boolean;
  onClick?: (collapsed: boolean) => void;
}

export const ItemCard = memo(
  forwardRef<HTMLDivElement, ItemCardProps>((props, forwardedRef) => {
    const {
      className,
      children,
      onClick,
      classNames = {},
      borderCard,
      headerLabel,
      footerCollapse,
      collapsedValue,
      disabled,
      ...restProps
    } = props;
    const childrenList = React.Children.toArray(children) as { props: CardSlotProps }[];

    const nodeMap = childrenList.reduce((acc, child) => {
      const { slot } = child.props || {};
      if (slot) {
        acc[slot] = child as React.ReactNode;
      }
      return acc;
    }, {} as Record<Slot, React.ReactNode>);

    const footerRef = useRef<HTMLDivElement>(null);
    const [footerHeight, setFooterHeight] = useState(0);
    const [innerCollapsed, setCollapsedValue] = useState(false);

    const footerStyle = innerCollapsed ? { height: footerHeight } : { height: 0 };

    const handleClick = () => {
      if (disabled) return;

      // 开启了 footerCollapse，但是没有传 collapsedValue，那么就是内部控制
      if (footerCollapse && !('collapsedValue' in props)) {
        setCollapsedValue(!innerCollapsed);
      }
      onClick?.(!innerCollapsed);
    };

    // 当 Footer 变化时，重新计算 Footer 的高度
    useResizeObserver(footerRef, () => {
      if (footerCollapse) {
        setFooterHeight(footerRef.current?.offsetHeight || 0);
      }
    });

    useEffect(() => {
      if (footerCollapse) {
        setCollapsedValue(!!collapsedValue);
      }
    }, [collapsedValue]);

    return (
      <DecoratorWithHeader showDecorator={!!headerLabel} label={headerLabel}>
        <div
          data-slot="item-card-base"
          ref={forwardedRef}
          className={cn(
            'w-full border-t-[1px] border-solid',
            borderCard && 'child:px-[20px] rounded-[20px] border-secondary-line border',
            headerLabel && 'rounded-t-none',
            innerCollapsed && 'border-primary',
            className,
            disabled && 'opacity-50',
          )}
          {...restProps}
        >
          <Condition if={nodeMap.badge}>
            <div
              {...slots.badge}
              className={cn(
                'text-[12px] font-medium leading-[16px] text-[#333] inline-block -translate-x-[20px]', // 和下面的 px-[20px] 对应
                classNames.badge,
              )}
            >
              {nodeMap.badge}
            </div>
          </Condition>
          <div
            {...slots.headerContentWrapper}
            className={cn(
              'py-[16px] px-[20px] cursor-pointer',
              classNames.headerContentWrapper,
              nodeMap.badge && 'pt-[10px]',
              disabled && 'cursor-default',
            )}
            onClick={handleClick}
          >
            <Condition if={nodeMap.header}>
              <div
                {...slots.header}
                className={cn('text-[16px] font-bold leading-[22px] text-[#828282]', classNames.header)}
              >
                {nodeMap.header}
              </div>
            </Condition>
            <div {...slots.content} className={cn('flex gap-[16px]', classNames.content)}>
              {nodeMap.content ? (
                nodeMap.content
              ) : (
                <>
                  <Condition if={nodeMap.picture}>{nodeMap.picture}</Condition>
                  <div className="flex w-full flex-col items-start gap-[8px]">
                    <Condition if={nodeMap.contentHeader}>
                      <div
                        {...slots.contentHeader}
                        className={cn('flex w-full justify-between', classNames.contentHeader)}
                      >
                        {nodeMap.contentHeader}
                      </div>
                    </Condition>
                    <Condition if={nodeMap.contentDescription}>
                      <div {...slots.contentDescription} className={classNames.contentDescription}>
                        {nodeMap.contentDescription}
                      </div>
                    </Condition>
                    <Condition if={nodeMap.contentFooter}>
                      <div {...slots.contentFooter} className={classNames.contentFooter}>
                        {nodeMap.contentFooter}
                      </div>
                    </Condition>
                  </div>
                </>
              )}
            </div>
          </div>
          <Condition if={nodeMap.footer}>
            <div
              {...slots.contentFooter}
              className={cn('transition-all duration-300 ease-out overflow-hidden', classNames.contentFooter)}
              style={footerStyle}
            >
              <div ref={footerRef}>{nodeMap.footer}</div>
            </div>
          </Condition>
        </div>
      </DecoratorWithHeader>
    );
  }),
);

ItemCard.displayName = 'ItemCard';
