import { useAtomValue } from 'jotai';
import { memo, MutableRefObject, useEffect } from 'react';
import { bookingPhoneState } from 'state/booking/bookingPhoneState';
import { CustomerQuestionKey } from 'types/question';
import { InputController, useInput } from 'widgets/Input/hooks/useInput';
import { Input } from 'widgets/Input/Input';
import { ErrorMessageLabel, Label } from 'widgets/Label';
import { PhoneInput } from '../components/Phone';
import { FormFieldProps } from '../FieldRegistry';

function isSamePhoneNumber(phoneNumber1?: string, phoneNumber2?: string) {
  if (!phoneNumber1?.trim() || !phoneNumber2?.trim()) {
    return false;
  }
  if (phoneNumber1 === phoneNumber2) {
    return true;
  }
  return phoneNumber1.replace(/\s+/g, '') === phoneNumber2.replace(/\s+/g, '');
}

export interface AdditionalContactRecord {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  id?: string;
}

interface AdditionalContactProps
  extends FormFieldProps<
    CustomerQuestionKey.PeopleAuthorizeToPickupPets | CustomerQuestionKey.EmergencyContact,
    AdditionalContactRecord
  > {
  inputsRef: MutableRefObject<MutableRefObject<InputController<AdditionalContactRecord>>[]>;
}

function AdditionalContactComponent(props: AdditionalContactProps) {
  const {
    title,
    typeKey,
    input,
    required,
    input: {
      state: { showValidateError, validateError },
    },
    fields,
    inputsRef,
  } = props;
  const clientPhoneNumber = useAtomValue(bookingPhoneState).phoneNumber;
  const firstNameInput = useInput(input.state.value?.firstName);
  const lastNameInput = useInput(input.state.value?.lastName);
  const phoneInput = useInput(input.state.value?.phoneNumber);
  const readOnly = input ? !!input.state.readOnly : undefined;

  useEffect(() => {
    if (showValidateError) {
      firstNameInput.showValidateError();
      lastNameInput.showValidateError();
      phoneInput.showValidateError();
    } else {
      firstNameInput.hideValidateError();
      lastNameInput.hideValidateError();
      phoneInput.hideValidateError();
    }
  }, [showValidateError]);

  useEffect(() => {
    const isEmergency = typeKey === CustomerQuestionKey.EmergencyContact;
    const isPickup = typeKey === CustomerQuestionKey.PeopleAuthorizeToPickupPets;
    input.state.validator = () => {
      const { valid: firstNameValid } = firstNameInput.state;
      const { valid: lastNameValid } = lastNameInput.state;
      const { valid: phoneValid, value: phoneValue } = phoneInput.state;
      const result =
        (typeof firstNameValid === 'boolean' ? firstNameValid : true) &&
        (typeof lastNameValid === 'boolean' ? lastNameValid : true) &&
        phoneValid
          ? null
          : 'Additional contact is invalid';
      if ((isEmergency || isPickup) && isSamePhoneNumber(phoneValue, clientPhoneNumber)) {
        return `${isEmergency ? 'Emergency' : 'Pickup'} contact phone number cannot be the same as client phone number`;
      }
      if (isPickup) {
        const emergencyContactIndex = fields.findIndex((field) => field.key === CustomerQuestionKey.EmergencyContact);
        const refEmergencyContact = inputsRef.current[emergencyContactIndex];
        if (refEmergencyContact) {
          const emergencyContactInput = refEmergencyContact.current?.state?.value?.phoneNumber;
          if (isSamePhoneNumber(emergencyContactInput, phoneValue)) {
            return 'Pickup contact phone number cannot be the same as emergency contact phone number';
          }
        }
      }
      if (isEmergency) {
        const pickupContactIndex = fields.findIndex(
          (field) => field.key === CustomerQuestionKey.PeopleAuthorizeToPickupPets,
        );
        const refPickupContact = inputsRef.current[pickupContactIndex];
        if (refPickupContact) {
          const pickupContactInput = refPickupContact.current?.state?.value?.phoneNumber;
          if (isSamePhoneNumber(pickupContactInput, phoneValue)) {
            return 'Emergency contact phone number cannot be the same as pickup contact phone number';
          }
        }
      }
      return result;
    };
    input.setValue({
      firstName: firstNameInput.state.value,
      lastName: lastNameInput.state.value,
      phoneNumber: phoneInput.state.value,
      id: input.state.value?.id,
    });
  }, [firstNameInput, lastNameInput, phoneInput, typeKey, fields, clientPhoneNumber, input.state.value?.id]);

  return (
    <div data-component-name="AdditionalContact" data-input-role="root" ref={input.ref}>
      <Label showRequiredIndicator={required}>{title}</Label>
      <div className="flex-between-start child:w-[calc(50%-8px)]">
        <Input
          className="input-moe"
          placeholder="First name"
          input={firstNameInput}
          showRequiredIndicator={required}
          validator={required ? 'notnull' : undefined}
          maxLength={100}
          readOnly={readOnly}
        />
        <Input
          className="input-moe"
          placeholder="Last name"
          input={lastNameInput}
          showRequiredIndicator={required}
          validator={required ? 'notnull' : undefined}
          maxLength={100}
          readOnly={readOnly}
        />
      </div>
      <PhoneInput
        className="input-moe mt-[8px]"
        placeholder="************"
        input={phoneInput}
        required={required}
        readOnly={readOnly}
      />
      {phoneInput.state.valid && !!validateError && (
        <ErrorMessageLabel className="text-left">{validateError}</ErrorMessageLabel>
      )}
    </div>
  );
}

export const AdditionalContact = memo(AdditionalContactComponent);
