import { WEEK_DAY_ABBREVIATION_NAMES } from 'utils/const';
import { createRadio } from '../components/Radio';

const options = WEEK_DAY_ABBREVIATION_NAMES.map((name, index) => ({
  value: index,
  name,
}));

export const PreferredDay = createRadio({
  getOptions: () => options,
  valueKey: 'value',
  renderKey: 'name',
  displayName: 'PreferredDay',
  itemClassName: 'capitalize',
  multiply: true,
});
