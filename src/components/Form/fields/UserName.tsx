import { Input, InputProps } from '@moego/client-lib-widgets/dist/Input/Input';
import { Label } from '@moego/client-lib-widgets/dist/Label';
import { forwardRef, memo } from 'react';
import { Validator } from 'utils/validator';
import { ErrorMessageLabel } from 'widgets/Label';

export interface UserNameValue {
  firstName: string;
  lastName: string;
}

export interface UserNameProps extends Omit<InputProps, 'value' | 'onChange'> {
  value?: UserNameValue;
  onChange?: (value: UserNameValue) => void;
  errorMessage?: string;
  label?: string;
  required?: boolean;
}

function UserNameComponent(props: UserNameProps, ref: React.Ref<HTMLInputElement>) {
  const { label = 'Your name', required, value, errorMessage, onChange, validator, ...rest } = props;
  const { firstName = '', lastName = '' } = value || {};

  return (
    <div ref={ref}>
      {label && <Label showRequiredIndicator={required}>{label}</Label>}
      <div className="flex-between-start child:w-[calc(50%-8px)]">
        <Input
          {...rest}
          validator={validator as Validator}
          className="input-moe"
          placeholder="First"
          value={firstName}
          onInput={(e) => onChange?.({ lastName, firstName: e.target.value })}
        />
        <Input
          {...rest}
          validator={validator as Validator}
          className="input-moe"
          placeholder="Last"
          value={lastName}
          onInput={(e) => onChange?.({ firstName, lastName: e.target.value })}
        />
      </div>
      {errorMessage ? <ErrorMessageLabel>{errorMessage}</ErrorMessageLabel> : null}
    </div>
  );
}

export const UserName = memo(forwardRef(UserNameComponent)) as typeof UserNameComponent;
