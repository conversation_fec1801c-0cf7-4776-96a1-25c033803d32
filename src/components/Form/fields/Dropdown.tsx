import { createElement, memo, useMemo } from 'react';
import { PetQuestionKey } from 'types/question';
import { safeJsonParse } from 'utils/parse';
import { createDropdown } from '../components/Dropdown';
import { FormFieldProps } from '../FieldRegistry';

function DropdownComponent(props: FormFieldProps<PetQuestionKey, number | string>) {
  const Component = useMemo(() => {
    const options = safeJsonParse<string[]>(props.extra, []).map((i) => ({ id: i, name: i }));
    return createDropdown({
      getOptions: () => options,
      valueKey: 'id',
      renderKey: 'name',
      displayName: props.typeKey.toUpperCase(),
    });
  }, []);
  return createElement(Component, props);
}

export const Dropdown = memo(DropdownComponent);
