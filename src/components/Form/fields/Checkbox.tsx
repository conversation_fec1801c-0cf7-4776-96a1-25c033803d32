import { createElement, memo, useMemo } from 'react';
import { PetQuestionKey } from 'types/question';
import { safeJsonParse } from 'utils/parse';
import { createRadio } from '../components/Radio';
import { FormFieldProps } from '../FieldRegistry';

function CheckboxComponent(props: FormFieldProps<PetQuestionKey, number | string>) {
  const Component = useMemo(() => {
    const options = safeJsonParse<string[]>(props.extra, []).map((i) => ({ id: i, name: i }));
    return createRadio({
      getOptions: () => options,
      valueKey: 'id',
      renderKey: 'name',
      displayName: 'Checkbox',
      multiply: true,
    });
  }, []);
  return createElement(Component, props);
}

export const Checkbox = memo(CheckboxComponent);
