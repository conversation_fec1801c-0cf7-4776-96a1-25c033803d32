import { memo, useEffect } from 'react';
import { PetEmergencyContactRecord } from 'types/pet';
import { CustomerQuestionKey } from 'types/question';
import { useInput } from 'widgets/Input/hooks/useInput';
import { Input } from 'widgets/Input/Input';
import { PhoneInput } from '../components/Phone';
import { FormFieldProps } from '../FieldRegistry';

function EmergencyContactComponent({
  input,
  required,
  input: {
    state: { showValidateError },
  },
}: FormFieldProps<CustomerQuestionKey.EmergencyContact, PetEmergencyContactRecord>) {
  const nameInput = useInput(input.state.value?.emergencyContactName);
  const phoneInput = useInput(input.state.value?.emergencyContactPhone);
  const readOnly = input ? !!input.state.readOnly : undefined;

  useEffect(() => {
    if (showValidateError) {
      nameInput.showValidateError();
      phoneInput.showValidateError();
    } else {
      nameInput.hideValidateError();
      phoneInput.hideValidateError();
    }
  }, [showValidateError]);

  useEffect(() => {
    input.state.validator = () => {
      const { valid: nameValid } = nameInput.state;
      const { valid: phoneValid } = phoneInput.state;
      return (typeof nameValid === 'boolean' ? nameValid : true) && phoneValid ? null : 'Emergency contact is invalid';
    };
    input.setValue({
      emergencyContactName: nameInput.state.value,
      emergencyContactPhone: phoneInput.state.value,
    });
  }, [nameInput, phoneInput]);

  return (
    <div data-component-name="EmergencyContact" data-input-role="root" ref={input.ref}>
      <Input
        className="input-moe"
        label="Emergency contact"
        placeholder="Contact name"
        input={nameInput}
        showRequiredIndicator={required}
        validator={required ? 'notnull' : undefined}
        maxLength={100}
        readOnly={readOnly}
      />
      <PhoneInput
        className="input-moe mt-[8px]"
        placeholder="************"
        input={phoneInput}
        required={required}
        readOnly={readOnly}
      />
    </div>
  );
}

export const EmergencyContact = memo(EmergencyContactComponent);
