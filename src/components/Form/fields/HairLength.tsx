import { useAtomValueRef } from '@moego/client-lib-jotai/dist/useAtomRef';
import { bookingPetOptionsState } from 'state/question/state';
import { createDropdown } from '../components/Dropdown';

export const HairLength = createDropdown({
  getOptions: () => useAtomValueRef(bookingPetOptionsState).current.data?.petHairLengthList ?? [],
  valueKey: 'name',
  renderKey: 'name',
  displayName: 'HairLength',
});
