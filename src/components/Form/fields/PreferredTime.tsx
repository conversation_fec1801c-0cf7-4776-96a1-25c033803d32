import { useAtomValue } from 'jotai';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { businessTimeFormatState } from 'state/business/state';
import { BusinessTimeFormatType } from 'types/business';
import { PetQuestionKey } from 'types/question';
import { Dropdown } from '../components/Dropdown';
import { FormFieldProps } from '../FieldRegistry';

interface Option {
  time: number;
  name: string;
}

const options: { [format in BusinessTimeFormatType]?: Option[] } = {};

function getOptions(format: BusinessTimeFormatType = BusinessTimeFormatType['24H']): Option[] {
  const HOURS_IN_HALF_DAY = 12;
  if (!options[format]) {
    const is24Hour = format === BusinessTimeFormatType['24H'];
    const generateOptions = (startHour: number, period: string) =>
      [...Array(HOURS_IN_HALF_DAY)].map((_, i) => {
        const hour = startHour + i;
        return {
          time: hour,
          name: is24Hour ? `${hour}:00` : `${i === 0 ? HOURS_IN_HALF_DAY : i}:00 ${period}`,
        };
      });

    options[format] = [...generateOptions(0, 'am'), ...generateOptions(HOURS_IN_HALF_DAY, 'pm')];
  }
  return options[format]!;
}

function PreferredTimeComponent({ input, required, defaultValue }: FormFieldProps<PetQuestionKey, [number, number]>) {
  const timeFormatType = useAtomValue(businessTimeFormatState)?.timeFormatType;
  const [times, setTimes] = useState([-1, 25]);
  const [start, end] = times;

  const startOptions = useMemo(() => {
    return getOptions(timeFormatType).filter((i) => i.time < end);
  }, [end, timeFormatType]);
  const endOptions = useMemo(() => {
    return getOptions(timeFormatType).filter((i) => i.time > start);
  }, [start, timeFormatType]);

  const setStart = useCallback(({ time }: Option) => setTimes(([, end]) => [time, end]), []);
  const setEnd = useCallback(({ time }: Option) => setTimes(([start]) => [start, time]), []);

  useEffect(() => {
    const [start, end] = times;
    if (start > -1 && end < 25) {
      input.setValue([start * 60, end * 60]);
    }
  }, [times]);

  useEffect(() => {
    if (defaultValue) {
      const [start, end] = defaultValue;
      setTimes([start / 60, end / 60]);
    }
  }, [defaultValue]);

  return (
    <div
      data-component-name="PreferredTime"
      data-input-role="root"
      className="flex items-end justify-between leading-[8px] gap-[16px]"
      ref={input.ref}
    >
      <Dropdown
        title="Preferred time of the day"
        required={required}
        options={startOptions}
        onChange={setStart}
        renderKey="name"
      >
        {startOptions.find((i) => i.time === start)?.name ?? ''}
      </Dropdown>
      <span className="leading-[56px]">-</span>
      <Dropdown required={required} options={endOptions} onChange={setEnd} renderKey="name">
        {endOptions.find((i) => i.time === end)?.name ?? ''}
      </Dropdown>
    </div>
  );
}

export const PreferredTime = memo(PreferredTimeComponent);
