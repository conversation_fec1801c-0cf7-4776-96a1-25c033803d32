import { useAtomValueRef } from '@moego/client-lib-jotai/dist/useAtomRef';
import { default as classNames } from 'classnames';
import { useWeightValidator } from 'hooks/useWeightValidator';
import { ChangeEvent, memo, useCallback } from 'react';
import { businessPreferenceState } from 'state/business/state';
import { formatWeightUnit, RE_INPUT_AMOUNT, WeightUnit } from 'utils/format';
import { Input, InputProps } from 'widgets/Input/Input';

export interface WeightInputProps
  extends Omit<InputProps, 'pattern' | 'showRequiredIndicator' | 'suffix' | 'type' | 'validator'> {
  required?: boolean;
}

function WeightInputComponent({ required, className, ...props }: WeightInputProps) {
  const validator = useWeightValidator(required);
  const suffix = formatWeightUnit(
    (useAtomValueRef(businessPreferenceState).current?.data?.unitOfWeight as WeightUnit) ?? null,
  );

  const onInput = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    e.target.value = value.match(RE_INPUT_AMOUNT)?.[0] || '';
  }, []);

  return (
    <Input
      {...props}
      validator={validator}
      showRequiredIndicator={required}
      suffix={suffix}
      className={classNames('input-moe px-0 mx-[20px]', className)}
      onInput={onInput}
      inputMode="decimal"
    />
  );
}

export const WeightInput = memo(WeightInputComponent);
