import { createElement, useMemo } from 'react';
import { CustomerQuestionKey } from 'types/question';
import { cn } from 'utils/classNames';
import { safeJsonParse } from 'utils/parse';
import { createRadio } from '../components/Radio';
import { FormFieldProps } from '../FieldRegistry';

interface Option {
  id: number;
  sourceName: string;
}

export function ReferralSource(props: FormFieldProps<CustomerQuestionKey.ReferralSource, number>) {
  const options = useMemo(() => {
    const options_ = safeJsonParse<Option[]>(props.extra, []);
    return props.readOnly ? options_.filter((i) => i.id === props.defaultValue) : options_;
  }, [props.extra, props.readOnly]);

  const Component = useMemo(() => {
    return createRadio({
      getOptions: () => options,
      valueKey: 'id',
      renderKey: 'sourceName',
      displayName: 'ReferralSource',
      itemClassName: 'capitalize',
      activatedItemClassName: cn({
        'bg-[#F3F3F3] text-[#505050] border-secondary-line': props.readOnly,
      }),
      readOnly: props.readOnly,
    });
  }, []);
  return createElement(Component, props as FormFieldProps<CustomerQuestionKey.ReferralSource, number | string>);
}
