import { isNil } from 'lodash';
import { memo, useCallback, useEffect, useState } from 'react';
import { PetQuestionKey } from 'types/question';
import { Dropdown } from '../components/Dropdown';
import { FormFieldProps } from '../FieldRegistry';

export enum PreferredFrequencyType {
  ByDays = 0,
  ByWeeks = 1,
  ByMonth = 2,
}

interface Option {
  id: number;
  name: string;
}

const typeOptions = [
  { id: PreferredFrequencyType.ByDays, name: 'Day' },
  { id: PreferredFrequencyType.ByWeeks, name: 'Week' },
  { id: PreferredFrequencyType.ByMonth, name: 'Month' },
];

function createOptions(n: number, scale: number) {
  return [...Array(n)].map((_, i) => ({ id: (i + 1) * scale, name: (i + 1).toString() }));
}

const PreferredFrequencyOptions = new Map<PreferredFrequencyType, { id: number; name: string }[]>([
  [PreferredFrequencyType.ByDays, createOptions(30, 1)],
  [PreferredFrequencyType.ByWeeks, createOptions(30, 7)],
  [PreferredFrequencyType.ByMonth, createOptions(30, 30)],
]);

function PreferredFrequencyComponent({
  input,
  required,
  defaultValue,
}: FormFieldProps<PetQuestionKey, { preferredFrequencyDay: number; preferredFrequencyType: number }>) {
  const [frequency, setFrequency] = useState({ value: 0, type: PreferredFrequencyType.ByWeeks });
  const { value, type } = frequency;
  const options = PreferredFrequencyOptions.get(type) || [];
  const setValue = useCallback(({ id }: Option) => setFrequency((v) => ({ ...v, value: id })), []);
  const setType = useCallback(
    ({ id }: Option) => setFrequency({ value: PreferredFrequencyOptions.get(id)?.[0].id!, type: id }),
    [],
  );

  useEffect(() => {
    if (frequency.value > 0 && !isNil(frequency.type)) {
      input.setValue({
        preferredFrequencyDay: frequency.value,
        preferredFrequencyType: frequency.type,
      });
    }
  }, [frequency]);

  useEffect(() => {
    if (defaultValue) {
      setFrequency({
        value: defaultValue.preferredFrequencyDay,
        type: defaultValue.preferredFrequencyType,
      });
    }
  }, [defaultValue?.preferredFrequencyDay, defaultValue?.preferredFrequencyType]);

  return (
    <div
      className="flex items-end justify-between leading-[8px] gap-[16px]"
      data-component-name="PreferredFrequency"
      data-input-role="root"
      ref={input.ref}
    >
      <Dropdown
        title="Preferred frequency"
        required={required}
        options={options || []}
        onChange={setValue}
        renderKey="name"
        className=""
      >
        {options.find((i) => i.id === value)?.name}
      </Dropdown>
      <Dropdown required={required} options={typeOptions} onChange={setType} renderKey="name">
        {typeOptions.find((i) => i.id === type)?.name}
      </Dropdown>
    </div>
  );
}

export const PreferredFrequency = memo(PreferredFrequencyComponent);
