import { useBoolean } from '@moego/client-lib-hooks/dist/useBoolean';
import { useVirtualKeyboardStatus } from '@moego/client-lib-hooks/dist/useVirtualKeyboardStatus';
import { scrollToSafePosition } from '@moego/client-lib-utils/dist/node';
import { Icon } from '@moego/client-lib-widgets/dist/Icon';
import { Input, InputController } from '@moego/client-lib-widgets/dist/Input/Input';
import { DropdownSelector } from '@moego/client-lib-widgets/dist/Selector/DropdownSelector';
import classNames from 'classnames';
import { useAtomValue } from 'jotai';
import { ReferencePicture } from 'pages/Pet/PetBasicForm/ReferencePicture';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { bookingPetOptionsState } from 'state/question/state';
import { BookingOptionEntity } from 'types/entity';
import { Validator } from 'utils/validator';

interface CoatTypeProps {
  required?: boolean;
  input: InputController<string>;
  onFocusChange?: (focus: boolean) => void;
  className?: string;
  referenceUrl?: string;
  readOnly?: boolean;
}

const borderClassName = '!border-solid !border-moe-primary';

type OptionEntity = BookingOptionEntity['petHairLengthList'][number];

function Empty() {
  return <div className="text-center my-[32px] text-base-content/60">No coat type found</div>;
}

export const CoatType = (props: CoatTypeProps) => {
  const { required, input, onFocusChange, className, referenceUrl, readOnly } = props;
  const val = input.state.value ?? '';
  const [isFilterEnable, setIsFilterEnable] = useBoolean(false);
  const { petHairLengthList } = useAtomValue(bookingPetOptionsState).data || {};
  const coatOptions = useMemo(() => (Array.isArray(petHairLengthList) ? petHairLengthList : []), []);
  const filterOptions = useMemo(
    () => coatOptions.filter((i) => i.name.toLocaleLowerCase().includes(val.trim().toLowerCase())),
    [coatOptions, val],
  );

  const [focus, setFocus] = useBoolean(false);
  const keyboardEnabled = useVirtualKeyboardStatus();
  const needScroll = focus && keyboardEnabled;
  const dropdownRef = useRef<HTMLDivElement>(null);
  const coatSearchInputRef = useRef<HTMLInputElement>(null);

  const onSelect = useCallback(
    (v: OptionEntity) => {
      input.setValue(v.name);
      coatSearchInputRef.current?.blur();
    },
    [input],
  );

  const validator = useCallback(
    (val: string) => {
      const coatStr = val.trim();
      const empty = !coatStr.length;
      // 不必填，且为空
      if (empty) {
        return required ? 'Coat type is required' : null;
      }
      // 如果有输入，则校验输入值是否正确
      const hasMatched = coatOptions.some((i) => i.name.trim() === coatStr);
      return hasMatched ? null : 'Please select a coat type';
    },
    [coatOptions, required],
  );

  useEffect(() => {
    // 初始化时，主动触发校验一次
    input.setValue(val);
  }, []);

  useEffect(() => {
    if (needScroll) {
      return scrollToSafePosition(dropdownRef.current!);
    }
  }, [needScroll]);

  useEffect(() => {
    onFocusChange?.(focus);
  }, [focus]);

  return (
    <DropdownSelector
      ref={dropdownRef}
      options={isFilterEnable ? filterOptions : coatOptions}
      onChange={onSelect}
      className={classNames(
        'px-0 block',
        {
          'pointer-events-none': readOnly,
        },
        className,
      )}
      optionsClassName={classNames(
        'max-h-[40vh] web:max-h-[32vh] border !rounded-t-none shadow-[inset_0_1px_0_0_var(--secondary-line-color)] !border-t-0',
        borderClassName,
        input.state.showValidateError && input.state.validateError ? 'mt-[-28px]' : null,
      )}
      empty={Empty}
      open={focus}
      renderKey="name"
      itemAsFragment
    >
      <Input
        label="Coat type"
        input={input}
        ref={coatSearchInputRef}
        onFocus={setFocus.enable}
        onBlur={setFocus.disable}
        onInput={() => !isFilterEnable && setIsFilterEnable.enable()}
        validator={validator as Validator}
        className={focus ? 'z-[50]' : ''}
        inputClassName={classNames(
          'input-moe transition-all duration-200',
          focus ? [borderClassName, 'border !border-b-0 !rounded-b-none !bg-base-100'] : null,
          readOnly ? '!bg-[#F3F3F3] !text-[#505050]' : null,
        )}
        prefix={
          <Icon
            name="search"
            className={classNames(
              'w-[20px] h-[20px] transition-all duration-300',
              focus ? 'fill-primary' : 'fill-text-secondary',
            )}
          />
        }
        suffix={<ReferencePicture url={referenceUrl} className={readOnly ? '!text-[#828282]' : undefined} />}
        showRequiredIndicator={required}
      />
    </DropdownSelector>
  );
};
