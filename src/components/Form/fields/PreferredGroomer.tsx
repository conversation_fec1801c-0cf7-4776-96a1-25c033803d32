import { useMemo } from 'react';
import { safeJsonParse } from 'utils/parse';
import { createRadio } from '../components/Radio';

interface Option {
  id: number;
  firstName: string;
  lastName: string;
}

export const PreferredGroomer = createRadio({
  getOptions: (fields, extra) => {
    return useMemo(
      () =>
        safeJsonParse<Option[]>(extra, []).map(({ id, firstName, lastName }) => ({
          id,
          name: `${firstName} ${lastName}`,
        })),
      [],
    );
  },
  valueKey: 'id',
  renderKey: 'name',
  displayName: 'PreferredGroomer',
  itemClassName: 'capitalize',
});
