import { useAtomValueRef } from '@moego/client-lib-jotai/dist/useAtomRef';
import { bookingPetOptionsState } from 'state/question/state';
import { createRadio } from '../components/Radio';

export const Behavior = createRadio({
  getOptions: () => useAtomValueRef(bookingPetOptionsState).current.data?.behaviors ?? [],
  valueKey: 'name',
  renderKey: 'name',
  displayName: 'Behavior',
  multiply: false,
});
