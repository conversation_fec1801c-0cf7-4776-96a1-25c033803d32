import {
  createVaccineFilterByPetType,
  getRequiredVaccineIds,
} from '@moego/client-lib-components/dist/Vaccine/useVaccineValues';
import { useValidateDate } from '@moego/client-lib-hooks/dist/useValidateDate';
import { useAtomValueRef } from '@moego/client-lib-jotai/dist/useAtomRef';
import { PetVaccineRecord } from '@moego/client-lib-types/dist/pet';
import { default as classNames } from 'classnames';
import { default as dayjs } from 'dayjs';
import { useBusinessDay } from 'hooks/useBizValidateDate';
import { useAtomValue } from 'jotai';
import { CareTypeMapNumberToString, PetTypeMapNumberToString } from 'pages/Pet/utils';
import { useVaccineFieldsConfig } from 'pages/UserPet/hooks/useVaccineFieldsConfig';
import { Fragment, memo, useCallback, useEffect, useMemo, useState } from 'react';
import { toast } from 'react-hot-toast';
import { didLoginState } from 'state/account/state';
import { bdServiceItemTypeDerivedAtom } from 'state/boardingDaycare/serviceItemTypeAtom';
import { bookingPetOptionsState, bookingPetQuestionsState } from 'state/question/state';
import { PetVaccineEntity } from 'types/entity';
import { PetQuestionKey } from 'types/question';
import { DATE_FORMAT_EXCHANGE } from 'utils/const';
import { ErrorMessageLabel } from 'widgets/Label';
import { DateInput } from '../components/DateInput';
import { Dropdown } from '../components/Dropdown';
import { Upload } from '../components/Upload';
import { FormFieldProps } from '../FieldRegistry';

const mimeToExtensionMap: Record<string, string> = {
  'application/vnd.ms-excel': '.xls',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
  'application/msword': '.doc',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
  'application/pdf': '.pdf',
  'image/jpeg': '.jpg,.jpeg',
  'image/png': '.png',
  'image/gif': '.gif',
};

const allowedMIMEVaccineFormats = [
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/pdf',
  'image/jpeg',
  'image/png',
  'image/gif',
];

const allowedVaccineFormats = allowedMIMEVaccineFormats.map((mimeType) => mimeToExtensionMap[mimeType]).join(',');

function VaccineComponent({
  extra,
  fields,
  input,
  required,
  input: {
    state: { showValidateError, valid },
  },
  petTypeId,
}: FormFieldProps<PetQuestionKey, PetVaccineRecord[]>) {
  const today = useMemo(() => dayjs(), []);
  const validateDate = useValidateDate(DATE_FORMAT_EXCHANGE);
  const makeDay = useBusinessDay(DATE_FORMAT_EXCHANGE);
  /**
   * 这个组件是 vaccine question 展示后才用，所以无需过滤逻辑，直接从完整的 question list 里面去取即可
   */
  const petQuestionList = useAtomValueRef(bookingPetQuestionsState).current.data;

  const isExistClient = useAtomValue(didLoginState);

  const fieldsConfig = useVaccineFieldsConfig();

  const options =
    useAtomValueRef(bookingPetOptionsState).current.data?.vaccines?.filter(
      createVaccineFilterByPetType(PetTypeMapNumberToString[petTypeId!], petQuestionList),
    ) || [];
  const careType = useAtomValue(bdServiceItemTypeDerivedAtom);

  const requiredTypes = getRequiredVaccineIds(
    petQuestionList,
    CareTypeMapNumberToString[careType],
    PetTypeMapNumberToString[petTypeId!],
    options,
  );
  const [vaccines, setVaccines] = useState<(PetVaccineRecord | null)[]>(() => {
    if (input.state.value?.length) {
      // 如果是一个非空数组，才使用这个值
      return input.state.value;
    }
    if (requiredTypes.length) {
      return requiredTypes.map<PetVaccineRecord>((vaccineId) => ({ vaccineId, expirationDate: '' }));
    }
    return [null];
  });

  const setVaccine = useCallback(
    (v: Partial<PetVaccineRecord>, index: number) =>
      setVaccines((vaccines) => {
        if (!vaccines[index]) {
          vaccines[index] = { vaccineId: 0, expirationDate: '' };
        }
        let vaccine: PetVaccineRecord | null = { ...vaccines[index]!, ...v };
        if (!vaccine.vaccineId && !vaccine.expirationDate) {
          vaccine = null;
        }
        vaccines[index] = vaccine;
        return [...vaccines];
      }),
    [],
  );

  const onAddVaccine = useCallback(() => setVaccines((v) => [...v, null]), []);

  const onDeleteVaccine = useCallback(
    (index: number) =>
      setVaccines((v) => {
        v.splice(index, 1);
        return [...v];
      }),
    [],
  );

  const onSetVaccineId = useCallback(
    ({ id: vaccineId }: PetVaccineEntity, index: number) => setVaccine({ vaccineId }, index),
    [],
  );
  const onSetExpirationDate = useCallback((v: string, index: number) => setVaccine({ expirationDate: v }, index), []);
  const onSetVaccineDocument = useCallback((v: string, index: number) => setVaccine({ documentUrls: [v] }, index), []);

  const beforeUpload = useCallback((files: File[]) => {
    const [file] = files;
    const allowed = allowedVaccineFormats.split(',');
    const ext = file.name.split('.').pop()?.toLowerCase();
    if (!ext || !allowed.includes(`.${ext}`)) {
      files.splice(0, 1);
      toast.error(`File format ${ext ? `".${ext} "` : ''}not allowed`);
    }
  }, []);

  input.state.validator = useCallback(
    (vaccines: (PetVaccineRecord | null)[]) => {
      const valid = vaccines?.every((v) => {
        if (!v || !v.vaccineId) {
          return !required;
        }
        if (required) {
          // 如果必填则分别校验expirationDate和documentUrls
          if (fieldsConfig.showExpirationDate && fieldsConfig.requireExpirationDate) {
            if (!v.expirationDate) {
              return false;
            }
            const { expirationDate } = v;
            if (expirationDate) {
              if (!validateDate(expirationDate)) {
                return false;
              }
              if (required && makeDay(expirationDate).isBefore(today, 'day')) {
                return false;
              }
            }
          }
          if (fieldsConfig.showVaccineDocument && fieldsConfig.requireVaccineDocument) {
            return !!v.documentUrls?.length;
          }
        }
        return true;
      });
      return valid ? null : 'Vaccine fields are required';
    },
    [required, fieldsConfig, validateDate, makeDay, isExistClient, today],
  );

  useEffect(() => input.setValue(vaccines as PetVaccineRecord[]), [vaccines]);

  const last = vaccines[vaccines.length - 1];
  const addDisabled = useMemo(() => {
    // 这里保持原有逻辑
    if (!last || !last.vaccineId) {
      return true;
    }
    // 如果展示疫苗过期时间且必填，则不能添加疫苗
    if (fieldsConfig.showExpirationDate && fieldsConfig.requireExpirationDate) {
      return !last.expirationDate;
    }
    // 如果展示疫苗文档且必填，则不能添加疫苗
    if (fieldsConfig.showVaccineDocument && fieldsConfig.requireVaccineDocument) {
      return !last?.documentUrls?.length;
    }
    return false;
  }, [last, fieldsConfig]);

  return (
    <div data-component-name="Vaccine" data-input-role="root" className="space-y-[8px]" ref={input.ref}>
      {vaccines?.map((vaccine, index) => {
        const expirationDateTip =
          showValidateError && !valid && !vaccine?.expirationDate ? 'Vaccine expiration date is required' : undefined;
        const inValidateDateTip =
          vaccine?.expirationDate && (!validateDate(vaccine?.expirationDate) ? 'Invalid date' : undefined);
        const isBeforeToday =
          required && vaccine?.expirationDate && makeDay(vaccine?.expirationDate).isBefore(today, 'day')
            ? 'Expiration date cannot be earlier than today'
            : undefined;
        const tip = expirationDateTip ?? inValidateDateTip ?? isBeforeToday;

        return (
          <Fragment key={index}>
            {index === 0 ? null : <hr className="!mt-[16px] !mb-[4px]" />}
            <Dropdown
              title={`Vaccine${index ? index + 1 : ''}`}
              required={required}
              options={options}
              onChange={(v) => onSetVaccineId(v, index)}
              renderKey="name"
              readonly={!!requiredTypes[index]}
              className={classNames(!!requiredTypes[index] ? '!text-base-primary' : null)}
            >
              {vaccine ? options.find((i) => i.id === vaccine.vaccineId)?.name : null}
            </Dropdown>
            {showValidateError && !valid && !vaccine?.vaccineId ? (
              <ErrorMessageLabel className="!mt-0">Vaccine type is required</ErrorMessageLabel>
            ) : null}

            {fieldsConfig.showExpirationDate ? (
              <>
                <DateInput
                  title="Expiration date"
                  defaultValue={vaccine?.expirationDate}
                  required={fieldsConfig.requireExpirationDate}
                  onChange={(v) => onSetExpirationDate(v, index)}
                />
                {/* 如果展示疫苗过期时间且必填，则显示错误提示，如果不展示疫苗过期时间，则不显示expired date 相关校验提示信息 */}
                {tip && <ErrorMessageLabel className="!mt-0">{tip}</ErrorMessageLabel>}
              </>
            ) : null}

            {fieldsConfig.showVaccineDocument ? (
              <>
                <Upload
                  title="Document (Word/PDF/Image)"
                  required={fieldsConfig.requireVaccineDocument}
                  onChange={(v) => onSetVaccineDocument(v, index)}
                  defaultValue={vaccine?.documentUrls?.[0]}
                  beforeUpload={beforeUpload}
                  accept={allowedMIMEVaccineFormats.join(',')}
                />
                {showValidateError &&
                !valid &&
                fieldsConfig.requireVaccineDocument &&
                !vaccine?.documentUrls?.length ? (
                  <ErrorMessageLabel className="!mt-0">Vaccine document is required</ErrorMessageLabel>
                ) : null}
              </>
            ) : null}

            {index > 0 && !requiredTypes[index] ? (
              <button className="btn btn-moe-normal btn-danger block" onClick={() => onDeleteVaccine(index)}>
                Delete
              </button>
            ) : null}
          </Fragment>
        );
      })}

      <button
        className="btn btn-link btn-sm link block disabled:!bg-opacity-0 !px-0"
        onClick={onAddVaccine}
        disabled={addDisabled}
      >
        + Additional vaccine
      </button>
    </div>
  );
}

export const Vaccine = memo(VaccineComponent);
