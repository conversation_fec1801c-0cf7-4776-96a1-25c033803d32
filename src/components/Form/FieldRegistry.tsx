import { ComponentType, createElement, memo, MutableRefObject, useEffect, useRef } from 'react';
import { PetType } from 'types/pet';
import { CustomerQuestionKey, PetQuestionKey, QuestionType } from 'types/question';
import { get<PERSON><PERSON><PERSON><PERSON>, Validator } from 'utils/validator';
import { InputController, InputProps, useInput } from 'widgets/Input/Input';
import { Text, TextArea } from './components/Text';
import { AdditionalContact } from './fields/AdditionalContact';
import { Behavior } from './fields/Behavior';
import { Birthday } from './fields/Birthday';
import { Checkbox } from './fields/Checkbox';
import { Dropdown } from './fields/Dropdown';
import { Fixed } from './fields/Fixed';
import { Gender } from './fields/Gender';
import { HairLength } from './fields/HairLength';
import { PetImage } from './fields/Image';
import { Phone } from './fields/Phone';
import { PreferredDay } from './fields/PreferredDay';
import { PreferredFrequency } from './fields/PreferredFrequency';
import { PreferredGroomer } from './fields/PreferredGroomer';
import { PreferredTime } from './fields/PreferredTime';
import { Radio } from './fields/Radio';
import { ReferralSource } from './fields/ReferralSource';
import { Vaccine } from './fields/Vaccine';
import { FormQuestionItem } from './QuestionForm';

export interface FormFieldProps<
  K extends PetQuestionKey | CustomerQuestionKey,
  T,
  U extends QuestionType = QuestionType,
> {
  index: number;
  input: InputController<T>;
  type: U;
  typeKey: K;
  title?: string;
  defaultValue?: T;
  required?: boolean;
  placeholder?: string;
  fields: FormQuestionItem[];
  extra?: string;
  petTypeId?: PetType;
  readOnly?: boolean;
}

type FormFieldComponent =
  | ComponentType<FormFieldProps<PetQuestionKey, any>>
  | ComponentType<FormFieldProps<CustomerQuestionKey, any>>;

const FormFieldsByKey = new Map<PetQuestionKey | CustomerQuestionKey, FormFieldComponent>([
  [PetQuestionKey.Gender, Gender],
  [PetQuestionKey.Birthday, Birthday],
  [PetQuestionKey.HairLength, HairLength],
  [PetQuestionKey.Fixed, Fixed],
  [PetQuestionKey.Behavior, Behavior],
  [PetQuestionKey.VaccineList, Vaccine],
  [PetQuestionKey.PetImage, PetImage],
  [PetQuestionKey.HealthIssues, TextArea],
  [PetQuestionKey.VetPhone, Phone],
  [
    CustomerQuestionKey.EmergencyContact,
    AdditionalContact as unknown as ComponentType<FormFieldProps<CustomerQuestionKey, any>>,
  ],
  [
    CustomerQuestionKey.PeopleAuthorizeToPickupPets,
    AdditionalContact as unknown as ComponentType<FormFieldProps<CustomerQuestionKey, any>>,
  ],
  [CustomerQuestionKey.ReferralSource, ReferralSource as ComponentType<FormFieldProps<CustomerQuestionKey, any>>],
  [CustomerQuestionKey.PreferredGroomer, PreferredGroomer],
  [CustomerQuestionKey.PreferredFrequency, PreferredFrequency],
  [CustomerQuestionKey.PreferredDayOfTheWeek, PreferredDay],
  [CustomerQuestionKey.PreferredTimeOfTheDay, PreferredTime],
]);

const FormFieldsByType = new Map<QuestionType, FormFieldComponent>([
  [QuestionType.ShortText, Text],
  [QuestionType.LongText, TextArea],
  [QuestionType.Dropdown, Dropdown],
  [QuestionType.Radio, Radio],
  [QuestionType.Checkbox, Checkbox],
]);

export interface FormExtraProps extends Pick<InputProps, 'maxLength'> {}
const FormExtraPropsByKey = new Map<PetQuestionKey | CustomerQuestionKey, FormExtraProps>([
  [PetQuestionKey.VetName, { maxLength: 100 }],
  [PetQuestionKey.VetAddress, { maxLength: 100 }],
]);

const ignoreFields = new Set<string>([PetQuestionKey.VaccineDocument]);

function FormFieldComponent<Q extends PetQuestionKey | CustomerQuestionKey, T = string | number>(
  props: Omit<FormFieldProps<Q, T>, 'input'> & {
    inputsRef: MutableRefObject<MutableRefObject<InputController<T>>[]>;
  },
) {
  if (ignoreFields.has(props.typeKey)) {
    return null;
  }

  let field = FormFieldsByKey.get(props.typeKey) || FormFieldsByType.get(props.type);
  if (!field) {
    console.warn(`Unknown field type "${props.type}" for key "${props.typeKey}"`);
    field = Text as FormFieldComponent;
  }

  // 有些字段不需要特殊的 field，但需要特殊的 props，eg. vet name & vet address 需要限制 maxLength
  const extraProps = FormExtraPropsByKey.get(props.typeKey);

  const input = useInput<T>(props.defaultValue, {
    validator: props.required ? (getValidator('notnull') as Validator<T>) : undefined,
  });
  const inputRef = useRef<InputController<T>>();
  inputRef.current = input;

  useEffect(() => {
    if (props.inputsRef.current) {
      props.inputsRef.current[props.index] = inputRef as MutableRefObject<InputController<T>>;
    }
  }, []);

  return createElement(field as ComponentType<Partial<FormFieldProps<PetQuestionKey | CustomerQuestionKey, any>>>, {
    ...props,
    ...extraProps,
    input: input as InputController<unknown>,
  });
}

export const FormField = memo(FormFieldComponent);
