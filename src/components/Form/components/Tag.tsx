import { forwardRef, memo } from 'react';
import { cn } from 'utils/classNames';
import { Condition } from 'widgets/Condition';
import { Icon, IconType } from 'widgets/Icon';
import { ErrorMessageLabel, Label } from 'widgets/Label';

type TagValue = string | string[];
type TagOption = { label: string; value: string };

export interface TagProps {
  label?: string;
  errorMessage?: string;
  required?: boolean;
  requiredDisplayOverride?: boolean;
  itemClassName?: string;
  optionsClassName?: string;
  className?: string;
  isMultiple?: boolean;
  options?: TagOption[];
  value?: TagValue;
  onChange?: (value: TagValue) => void;
}

export const Tag = memo(
  forwardRef<HTMLDivElement, TagProps>((props, ref) => {
    const {
      isMultiple,
      label,
      className,
      errorMessage,
      required,
      requiredDisplayOverride,
      itemClassName,
      optionsClassName,
      options = [],
      value,
      onChange,
    } = props;

    const handleClick = (item: TagOption) => {
      const newValue = item.value;
      if (isMultiple) {
        const current = (value || []) as string[];
        onChange?.(current.includes(newValue) ? current.filter((v) => v !== newValue) : [...current, item.value]);
      } else {
        onChange?.(newValue === value ? '' : newValue);
      }
    };

    return (
      <div className={className} ref={ref}>
        {label && <Label showRequiredIndicator={requiredDisplayOverride ?? required}>{label}</Label>}

        <div className={cn('flex', optionsClassName)}>
          {options.map((item) => {
            const active = isMultiple ? ((value || []) as string[]).includes(item.value) : value === item.value;
            return (
              <button
                key={item.value}
                onClick={() => handleClick(item)}
                className={cn(
                  'btn rounded-full h-[40px] border-[1px] border-solid font-normal text-[16px]',
                  active
                    ? 'bg-primary-light hover:bg-primary-light border-primary hover:border-primary text-text-primary'
                    : 'btn-ghost border-[#E9ECEF] hover:bg-transparent hover:border-[#E9ECEF]',
                  itemClassName,
                )}
              >
                <span className="truncate max-w-[300px]">{item.label}</span>
                <Condition if={isMultiple && active}>
                  <Icon name={IconType.doneCheck} className="ml-[4px] w-[14px] h-[14px]" />
                </Condition>
              </button>
            );
          })}
        </div>
        {errorMessage ? <ErrorMessageLabel>{errorMessage}</ErrorMessageLabel> : null}
      </div>
    );
  }),
);
