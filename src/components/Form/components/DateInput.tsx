import { useBoolean } from '@moego/client-lib-hooks/dist/useBoolean';
import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import { transformDateFormat } from '@moego/client-lib-hooks/dist/useValidateDate';
import { default as classNames } from 'classnames';
import dayjs from 'dayjs';
import { usePreferenceDateFormat } from 'hooks/usePreferenceDateFormat';
import { KeyboardEvent, memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { PetQuestionKey } from 'types/question';
import { DATE_FORMAT_EXCHANGE } from 'utils/const';
import { Input, InputController } from 'widgets/Input/Input';
import { InputErrorMessageLabel, Label } from 'widgets/Label';
import { FormFieldProps } from '../FieldRegistry';
import { fieldInputClassNames, getDateFormat } from '../utils';

export type DateInputProps = Pick<FormFieldProps<PetQuestionKey, string>, 'title' | 'required' | 'defaultValue'> & {
  input?: InputController<string>;
  onChange?: (v: string) => void;
  readonly?: boolean;
};

function DateInputComponent({
  input,
  title,
  required,
  onChange,
  defaultValue = '',
  readonly = undefined,
}: DateInputProps) {
  const inputRef = useRef<HTMLInputElement>(null);
  const preferenceDateFormat = usePreferenceDateFormat();
  const dateFormat = getDateFormat(defaultValue);
  const preferenceDefaultValue = transformDateFormat(
    input?.state?.value || defaultValue,
    dateFormat,
    preferenceDateFormat,
  );
  const displayedDateFormat = useMemo(() => preferenceDateFormat.replaceAll('/', ' / '), [preferenceDateFormat]);

  const [chars, setChars] = useState(preferenceDefaultValue?.replaceAll('/', '') || '');
  const [focus, setFocus] = useBoolean(false);
  const readOnly = input ? !!input.state.readOnly : readonly;

  const hackAndroidData = useRef({ value: '', change: false });

  const [displayChars, placeholder] = useMemo<[string[], string[]]>(() => {
    const numbers = chars.split('').reverse();
    const result = displayedDateFormat.split('');
    let cursor = 0;
    while (numbers.length) {
      const n = numbers.pop();
      if (n && result[cursor] !== undefined) {
        while (result[cursor] === ' ' || result[cursor] === '/') {
          cursor++;
        }
        result[cursor] = n;
        cursor++;
      }
    }
    return [result.slice(0, cursor), displayedDateFormat.slice(cursor).split('')];
  }, [chars, displayedDateFormat]);

  const pop = () => setChars((chars) => chars.slice(0, -1));
  const push = (key: string) => setChars((chars) => (chars + key).slice(0, 8));

  const handleChange = useLatestCallback((e) => {
    hackAndroidData.current.change = true;
    hackAndroidData.current.value = e.value;
  });

  const handleAndroidChange = () => {
    /**
     * Hack for Android
     * 1. change 为 true，即触发了change事件，此时拿到最新的value并过滤掉非number类型的 push
     * 2. change 为 false，即触发了 keydown 事件，但是并未触发值的变化，则认为是 backspace 触发value pop
     */
    if (hackAndroidData.current.change && hackAndroidData.current.value) {
      if (/[0-9]/.test(hackAndroidData.current.value)) {
        push(hackAndroidData.current.value);
      }
      return;
    }
    pop();
  };

  const onKeyDown = useCallback(({ key }: KeyboardEvent<HTMLInputElement>) => {
    if (key !== 'Unidentified' && inputRef.current) {
      inputRef.current.value = '';
      if (/[0-9]/.test(key)) {
        push(key);
      } else if (key === 'Backspace') {
        pop();
      }
      return;
    }
    // Hack for stupid android keyboard only android will run the following function
    hackAndroidData.current.change = false;
    setTimeout(() => {
      handleAndroidChange();
    });
  }, []);

  const onFocusInput = useCallback(() => inputRef.current?.focus(), []);

  useEffect(() => {
    // 保存时候是 savedDateFormat
    const v = displayChars.join('').replaceAll(' ', '');
    const savedValue = transformDateFormat(v, preferenceDateFormat, DATE_FORMAT_EXCHANGE);
    const mergedValue = savedValue || v;
    // 当日期不合法时，savedValue 会被 transformDateFormat 设置为空字符串，这里需要重新设置为 v，让它走 validator 从而展示错误 block 用户，否则只会传一个空字符串
    input?.setValue(mergedValue);
    onChange?.(mergedValue);
  }, [displayChars, preferenceDateFormat]);

  useEffect(() => {
    if (focus) {
      inputRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, [focus]);

  useEffect(() => {
    if (input) {
      input.state.validator = (v: string = '') => {
        // 校验时候是 savedDateFormat
        const raw = v.replaceAll('-', '');
        const size = raw.length;
        const semiInput = size > 0 && size < 8;
        if (semiInput) {
          return 'Invalid date';
        }
        if (required && size < 8) {
          return 'This field is required';
        }
        if (size > 0 && !dayjs(v, DATE_FORMAT_EXCHANGE, true).isValid()) {
          return 'Invalid date';
        }
        return null;
      };
    }
  }, [input, required]);

  useEffect(() => {
    if (input && input.state.value === undefined) {
      input.state.value = '';
    }
  }, []);

  return (
    <div data-component-name="DateInput" data-input-role="root" className="relative" ref={input?.ref}>
      {title ? <Label showRequiredIndicator={required}>{title}</Label> : null}
      <div
        className={classNames(
          'child:w-[11px] child:inline-block relative',
          readOnly ? 'unclickable !bg-[#F3F3F3]' : '',
          fieldInputClassNames(focus, readOnly),
        )}
        onClick={onFocusInput}
      >
        {displayChars.map((i, index) => (
          <span key={index} className={classNames(i.trim() ? null : 'w-[5px]', readOnly ? 'text-[#505050]' : '')}>
            {i}
          </span>
        ))}
        {focus ? <div className="absolute h-[22px] w-[1px] bg-current top-[17px] flicker" /> : null}
        {placeholder.map((i, index) => (
          <span
            key={index}
            className={classNames(
              'text-center',
              i === 'M' ? 'text-[15px]' : null,
              i.trim() ? null : 'w-[5px]',
              readOnly ? 'text-[#505050]' : 'text-[#C8CBD2]',
            )}
          >
            {i}
          </span>
        ))}
      </div>
      <Input<string>
        ref={inputRef}
        onKeyDown={onKeyDown}
        onFocus={setFocus.enable}
        onBlur={setFocus.disable}
        maxLength={1}
        pattern="\d*"
        className="input-moe !absolute bottom-0 left-0 opacity-0"
        readOnly={readOnly}
        onChange={handleChange}
      />
      {input ? <InputErrorMessageLabel input={input} /> : null}
    </div>
  );
}

export const DateInput = memo(DateInputComponent);
