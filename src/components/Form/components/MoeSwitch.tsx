import { Switch } from '@headlessui/react';
import React from 'react';
import { cn } from 'utils/classNames';

export interface MoeSwitchProps {
  checked?: boolean;
  onChange?: (checked: boolean) => void;
}

export const MoeSwitch: React.FC<MoeSwitchProps> = (props) => {
  const { checked, onChange } = props;
  return (
    <Switch
      checked={checked}
      onChange={onChange}
      className={cn(
        checked ? 'bg-primary' : 'bg-[#E6E6E6]',
        'relative inline-flex h-[18px] w-[32px] shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out',
        'focus:outline-none focus-visible:ring-2  focus-visible:ring-white/75',
      )}
    >
      <span
        aria-hidden="true"
        className={cn(
          checked ? 'translate-x-[14px]' : 'translate-x-0',
          'pointer-events-none inline-block h-[14px] w-[14px] transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out',
        )}
      />
    </Switch>
  );
};
