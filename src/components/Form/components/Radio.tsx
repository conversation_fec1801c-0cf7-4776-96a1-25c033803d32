import { useLazyEntryNode } from '@moego/client-lib-hooks/dist/useEntryNode';
import { TabSelector, TabSelectorProps } from '@moego/client-lib-widgets/dist/Selector/TabSelector';
import { default as classNames } from 'classnames';
import {
  ForwardedRef,
  forwardRef,
  memo,
  MutableRefObject,
  RefAttributes,
  useCallback,
  useEffect,
  useMemo,
} from 'react';
import { CustomerQuestionKey, PetQuestionKey } from 'types/question';
import { cn } from 'utils/classNames';
import { safeJsonParse } from 'utils/parse';
import { Validator } from 'utils/validator';
import { InputErrorMessageLabel } from 'widgets/Label';
import { FormFieldProps } from '../FieldRegistry';
import { FormQuestionItem } from '../QuestionForm';

type RadioProps<T> = Pick<FormFieldProps<PetQuestionKey, number | string>, 'title' | 'required'> &
  Pick<
    TabSelectorProps<T>,
    'options' | 'onChange' | 'renderKey' | 'item' | 'itemAsFragment' | 'itemClassName' | 'children' | 'defaultValue'
  > & {
    multiply?: boolean;
    readOnly?: boolean;
    activatedItemClassName?: string;
  };

const RadioComponent = forwardRef(function RadioComponent<T>(
  {
    title,
    required,
    options,
    onChange,
    renderKey,
    multiply,
    item,
    itemClassName,
    itemAsFragment,
    children,
    defaultValue,
    readOnly,
    activatedItemClassName,
  }: RadioProps<T>,
  ref: ForwardedRef<HTMLDivElement>,
) {
  return (
    <TabSelector
      ref={ref as MutableRefObject<HTMLDivElement>}
      optionsClassName={cn('!mt-0 flex-wrap child:mr-[12px] child:mb-[6px]', {
        'cursor-not-allowed': readOnly,
      })}
      activatedItemClassName={cn(
        {
          'bg-primary-light text-primary hover:bg-primary-light': !multiply,
        },
        activatedItemClassName,
      )}
      options={options}
      onChange={onChange}
      renderKey={renderKey}
      label={title}
      itemClassName={classNames('min-w-[80px] whitespace-nowrap font-normal text-[16px]', itemClassName)}
      itemWrapperClassName={cn('rounded-full', {
        'pointer-events-none': readOnly,
      })}
      item={item}
      itemAsFragment={itemAsFragment}
      showRequiredIndicator={required}
      multiply={multiply}
      asFragment={false}
      defaultValue={defaultValue}
    >
      {children}
    </TabSelector>
  );
});

export const Radio = memo(RadioComponent) as unknown as <T>(
  props: RadioProps<T> & RefAttributes<HTMLDivElement>,
) => JSX.Element;

export interface CreateRadioReturn<C extends PetQuestionKey | CustomerQuestionKey> {
  (props: FormFieldProps<C, string | number>): JSX.Element;
  displayName: string;
  activatedItemClassName?: string;
}

export interface CreateRadioOptions<T, M extends boolean = false> {
  multiply?: M;
  getOptions: (fields: FormQuestionItem[], extra?: string) => T[];
  defaultValue?: T | T[];
  valueKey?: keyof T;
  renderKey?: keyof T;
  displayName?: string;
  itemClassName?: string;
  readOnly?: boolean;
  activatedItemClassName?: string;
}

export function createRadio<T, C extends PetQuestionKey | CustomerQuestionKey, M extends boolean = false>({
  getOptions,
  defaultValue: defaultValueProp,
  valueKey: valueKeyProp,
  renderKey: renderKeyProp,
  multiply,
  displayName,
  itemClassName,
  readOnly,
  activatedItemClassName,
}: CreateRadioOptions<T, M>): CreateRadioReturn<C> {
  const valueKey = valueKeyProp || ('id' as keyof T);
  const renderKey = renderKeyProp || ('name' as keyof T);
  const Component: CreateRadioReturn<C> = ({
    input,
    title,
    required,
    fields,
    extra,
  }: FormFieldProps<C, string | number>) => {
    const options = getOptions(fields, extra);

    const onChange = useCallback(
      (value: T | T[]) => {
        input.setValue(
          multiply
            ? JSON.stringify((value as T[]).map((i) => i[valueKey]))
            : ((value as T)[valueKey] as number | string),
        );
      },
      [input.setState],
    );

    const defaultValue = useMemo<T | T[] | undefined>(() => {
      const { value } = input.state;
      if (!value) {
        if (!multiply) {
          return defaultValueProp;
        }
        return undefined;
      }
      return multiply
        ? (safeJsonParse<string[]>(value as string, [])
            .map((i) => options.find((o) => o[valueKey]?.toString() === i?.toString()))
            .filter((i) => !!i) as T[])
        : options.find((i) => i[valueKey] === value);
    }, [defaultValueProp]);

    useEffect(() => {
      if (input.state.value === undefined) {
        input.state.value = multiply ? '[]' : '';
      }
    }, []);

    if (multiply && required) {
      input.state.validator = useCallback((v) => {
        return !v || v === '[]' ? 'This field is required' : undefined;
      }, []) as Validator;
    }

    return (
      <Radio
        ref={input.ref}
        options={options}
        onChange={onChange}
        renderKey={renderKey as keyof T}
        title={title}
        required={required}
        multiply={multiply}
        defaultValue={defaultValue}
        itemClassName={itemClassName}
        readOnly={readOnly}
        activatedItemClassName={activatedItemClassName}
      >
        <InputErrorMessageLabel input={input} portalEntry={useLazyEntryNode(() => input.ref.current!)} />
      </Radio>
    );
  };
  Component.displayName = `createRadio(${displayName ?? Component.name ?? 'Unknown'})`;
  return memo(Component) as CreateRadioReturn<C>;
}
