import { default as classNames } from 'classnames';
import dayjs from 'dayjs';
import {
  DATE_FORMAT_EXCHANGE,
  DATE_FORMAT_FULL_TIME,
  DATE_FORMAT_FULL_TIME_WITH_TIMEZONE,
  DATE_FORMAT_SLASH,
} from 'utils/const';

export function fieldInputClassNames(focus: boolean, outline?: boolean) {
  return classNames(
    'w-full h-[56px] rounded-[16px] px-[20px] py-[17px] text-[16px] leading-[20px]',
    'transition-all duration-200 border-[1px] border-solid',
    'bg-white border-secondary-line !border-opacity-100',
    focus ? 'border-opacity-100 !border-primary bg-opacity-0' : 'border-opacity-0',
  );
}

export function getDateFormat(date: string) {
  const formats = [DATE_FORMAT_SLASH, DATE_FORMAT_EXCHANGE, DATE_FORMAT_FULL_TIME_WITH_TIMEZONE, DATE_FORMAT_FULL_TIME];
  for (const format of formats) {
    if (dayjs(date, format, true).isValid()) {
      return format;
    }
  }
  return DATE_FORMAT_EXCHANGE;
}
