import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import { ForwardedRef, forwardRef, memo, MutableRefObject, useCallback, useEffect, useRef } from 'react';
import { BookingQuestionEntity } from 'types/entity';
import { CustomerQuestionKey, PetQuestionKey } from 'types/question';
import { isEmptyObject } from 'utils/is';
import { setRefProperties } from 'utils/ref';
import { InputController, validateInput } from 'widgets/Input/Input';
import { FormField } from './FieldRegistry';

export type FormQuestionItem = Pick<
  BookingQuestionEntity,
  'id' | 'question' | 'key' | 'isRequired' | 'extraJson' | 'questionType' | 'placeholder'
> & {
  readOnly?: boolean;
};

export interface QuestionFormProps {
  fields: FormQuestionItem[];
  className?: string;
  formRef: MutableRefObject<QuestionFormRef | null>;
  defaultValues?: FormValues;
  children?: React.ReactNode;
  petTypeId?: number;
  [dataSets: `data-${string}`]: unknown;
}

export interface FormValues {
  [key: string]: unknown;
}

export interface QuestionFormRef {
  inputsRef: MutableRefObject<MutableRefObject<InputController>[]>;
  validate: () => Promise<FormValues | null>;
  getValues: () => FormValues | null;
  setValues: (obj: FormValues) => void;
  setReadonly: (readonly: boolean) => void;
  setReadOnlyFields: (readonlyFields: string[]) => void;
}

function QuestionFormComponent(
  { fields, className, defaultValues, formRef, children, petTypeId, ...dataSets }: QuestionFormProps,
  ref: ForwardedRef<HTMLDivElement>,
) {
  const inputsRef = useRef<MutableRefObject<InputController>[]>([]);

  const validate = useCallback(async () => {
    const values: FormValues = {};
    for (let index = 0; index < inputsRef.current.length; index++) {
      const field = fields[index];
      const input = inputsRef.current[index]?.current;
      if (!input) {
        continue;
      }
      const value = await validateInput(input, false);
      if (value === undefined) {
        console.info(
          `[QuestionForm] Form field input validate failed for key "${field.key}" and name "${field.question}"`,
          input,
        );
        return;
      }
      values[field.key] = value;
    }
    return isEmptyObject(values) ? null : values;
  }, [fields]);

  const getValues = useCallback(() => {
    const values: FormValues = {};
    inputsRef.current.forEach((input, index) => {
      if (!input) {
        return;
      }
      const field = fields[index];
      if (!field) {
        return;
      }
      values[field.key] = input.current.state.value;
    });
    return isEmptyObject(values) ? null : values;
  }, [fields]);

  const setValues = useCallback(
    (obj: FormValues) => {
      inputsRef.current.forEach((input, index) => {
        if (!input) {
          return;
        }
        const field = fields[index];
        if (!field) {
          return;
        }
        const value = obj[field.key];
        input.current.setValue(value as any);
      });
    },
    [fields],
  );

  const setReadonly = useCallback((readonly: boolean) => {
    inputsRef.current.forEach((input) => input?.current?.setReadonly(readonly));
  }, []);

  const setReadOnlyFields = useLatestCallback((readonlyFields: string[]) => {
    readonlyFields.forEach((readonlyField) => {
      inputsRef.current.forEach((input, index) => {
        if (!input) {
          return;
        }
        const field = fields[index];
        if (!field) {
          return;
        }
        const key = field.key;

        if (key === readonlyField) {
          input.current.setReadonly(true);
        }
      });
    });
  });

  useEffect(() => {
    setRefProperties(formRef, {
      inputsRef,
      validate,
      getValues,
      setValues,
      setReadonly,
      setReadOnlyFields,
    });
  }, [validate, getValues, setValues, setReadonly, setReadOnlyFields]);

  return (
    <div className={className} ref={ref} data-component-name="QuestionForm" {...dataSets}>
      {children}
      {fields.map(({ id, question, placeholder, questionType, key, isRequired, extraJson, readOnly }, index) => {
        return (
          <FormField
            fields={fields}
            inputsRef={inputsRef as MutableRefObject<MutableRefObject<InputController<unknown>>[]>}
            index={index}
            key={id}
            title={question}
            typeKey={key as PetQuestionKey | CustomerQuestionKey}
            type={questionType}
            placeholder={placeholder}
            // 设计交互逻辑： 如果问题为只读，则不显示必填标识
            required={readOnly ? false : !!isRequired}
            extra={extraJson}
            defaultValue={defaultValues?.[key]}
            petTypeId={petTypeId}
            readOnly={readOnly}
          />
        );
      })}
    </div>
  );
}

export const QuestionForm = memo(forwardRef(QuestionFormComponent));
