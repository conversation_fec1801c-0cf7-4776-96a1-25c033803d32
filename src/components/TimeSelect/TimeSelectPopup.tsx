import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import { useState } from 'react';
import { Button } from 'widgets/Button/Button';
import { Condition } from 'widgets/Condition';
import { SkeletonGroup } from 'widgets/Skeleton';
import { DefaultTransitions } from 'widgets/Transition/defaults';
import { OperationSection } from '../Layout/OperationSection';
import { TimeItem, TimeSelectItem } from './TimeSelectItem';

export interface TimeSelectPopupProps {
  show?: boolean;
  loading?: boolean;
  containerStyle?: React.CSSProperties;
  maskStyle?: React.CSSProperties;
  maskClassName?: string;
  onClose?: () => void;
  onBack?: () => void;
  isValidTime?: (value: Date) => boolean;
  initialValue?: Date;
  onSave?: (value: Date) => void;
}

export const TimeSelectPopup: React.FC<TimeSelectPopupProps> = (props) => {
  const {
    show,
    containerStyle,
    maskStyle,
    maskClassName,
    onClose,
    onBack,
    initialValue,
    onSave,
    isValidTime,
    loading,
  } = props;

  // edit value
  const [editValue, setEditValue] = useState<TimeItem | null>(null);
  const handleSave = useLatestCallback(() => {
    if (!editValue) {
      return;
    }
    onSave?.(editValue.value);
  });

  return (
    <OperationSection
      className="max-h-[90vh] web:mx-[var(--web-mx)] !pt-0 flex flex-col"
      containerStyle={containerStyle}
      show={show}
      transitionDelay={0}
      clickMaskToClose
      onClose={onClose}
      goBack={onBack}
      transition={DefaultTransitions.transitionY}
      maskClassName={maskClassName}
      mask
      portal
      headerSticky
      maskStyle={maskStyle}
      title="Select time"
    >
      <Condition if={loading}>
        <SkeletonGroup rows={6} height="52px" />
      </Condition>
      <Condition if={!loading && show}>
        <div className="flex-1 mx-[-20px] px-[20px] overflow-y-auto overflow-x-hidden">
          <TimeSelectItem
            initialValue={initialValue}
            isValidTime={isValidTime}
            activeValue={editValue}
            setActiveValue={setEditValue}
          />
        </div>

        <div className="flex justify-center mt-[10px]">
          <Button
            className="btn-moe-large btn btn-primary w-full btn-shadow"
            onClick={handleSave}
            disabled={!editValue}
          >
            Select time
          </Button>
        </div>
      </Condition>
    </OperationSection>
  );
};
