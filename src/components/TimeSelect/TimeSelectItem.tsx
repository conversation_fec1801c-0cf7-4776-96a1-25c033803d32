import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import dayjs, { Dayjs } from 'dayjs';
import { useTimeFormat } from 'hooks/useTimeFormat';
import { range } from 'lodash';
import { Empty } from 'pages/BoardingDaycare/components/Empty';
import { useEffect, useMemo } from 'react';
import { useLatest } from 'react-use';
import { getMinutes } from 'utils/date';
import { Switch } from 'widgets/SwitchCase';
import { TimeSelectBtn } from './TimeSelectBtn';

export type TimeItem = {
  text: string;
  value: Date;
};

export interface TimeSelectItemProps {
  isValidTime?: (value: Date) => boolean;
  initialValue?: Date | Dayjs;
  activeValue?: TimeItem | null;
  setActiveValue?: (value: TimeItem | null) => void;
  timeListDep?: TimeItem | string;
}

export const TimeSelectItem: React.FC<TimeSelectItemProps> = (props) => {
  const { initialValue, isValidTime, activeValue, setActiveValue, timeListDep } = props;

  const format = useTimeFormat();

  // time list
  const isValidTimeRef = useLatest(isValidTime ?? (() => true));
  const timeList = useMemo(() => {
    const startTime = dayjs().startOf('date');
    const resultList: TimeItem[] = range(0, 48).map((i) => {
      return {
        text: format(getMinutes(startTime.add(i * 30, 'minutes'))),
        value: startTime.add(i * 30, 'minutes').toDate(),
      };
    });
    return resultList.filter((item) => isValidTimeRef.current(item.value));
  }, [isValidTime, timeListDep]);

  // sync initialValue to edit value
  const setActiveValueCallback = useLatestCallback<Exclude<typeof setActiveValue, undefined>>((value) =>
    setActiveValue?.(value),
  );
  useEffect(() => {
    if (initialValue) {
      setActiveValueCallback?.(
        timeList.find((item) => getMinutes(dayjs(item.value)) === getMinutes(dayjs(initialValue))) ?? null,
      );
    }
  }, [initialValue, timeList, isValidTime]);

  return (
    <div className="flex justify-between items-start flex-wrap gap-x-[16px] gap-y-[16px] pb-[10px]">
      <Switch>
        <Switch.Case if={timeList.length}>
          {timeList.map((item) => (
            <TimeSelectBtn
              key={item.text}
              isActive={activeValue?.text === item.text}
              onClick={() => setActiveValue?.(item)}
            >
              {item.text}
            </TimeSelectBtn>
          ))}
        </Switch.Case>
        <Switch.Case else>
          <Empty
            title="Oops... No availability"
            description="No available time. Please pick another date."
            classNames={{
              description: 'text-mobile-body text-text-secondary',
              container: 'w-full flex justify-center items-center min-h-[312px]',
            }}
          />
        </Switch.Case>
      </Switch>
    </div>
  );
};
