import { DatePicker, DatePickerProps, Mode } from '@moego/client-lib-components/dist/DatePicker/DatePicker';
import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import dayjs from 'dayjs';
import { useEvaluationDatePickerContext } from 'hooks/useEvaluationDatePickerContext';
import { useEffect, useMemo, useState } from 'react';
import { cn } from 'utils/classNames';
import { DATE_FORMAT_EXCHANGE } from 'utils/const';
import { getExchangeFormattedDate } from 'utils/date';
import { Button } from 'widgets/Button/Button';
import { Condition } from 'widgets/Condition';
import { SkeletonGroup } from 'widgets/Skeleton';
import { DefaultTransitions } from 'widgets/Transition/defaults';
import { OperationSection } from '../Layout/OperationSection';

export type ValueType<M extends Mode = Mode> = M extends 'single'
  ? string
  : M extends 'range'
  ? [string, string]
  : string[];

export interface DatePickerWithPopupProps<M extends Mode = Mode, Value = ValueType<M>>
  extends Omit<React.ComponentProps<typeof DatePicker>, 'value' | 'onChange' | 'mode' | 'onDayChange'> {
  show?: boolean;
  onClose?: () => void;
  title?: string;
  mode?: M;
  value?: Value;
  onChange?: (value: Value) => void;
  onBack?: () => void;
  onSave?: (value: Value) => void;
  loading?: boolean;
  /** 需要等待请求返回完成才展示 */
  needInit?: boolean;
  /** 是否允许初始化时 onChange */
  changeAfterInit?: boolean;
  containerStyle?: React.CSSProperties;
  maskClassName?: string;
  maskStyle?: React.CSSProperties;
  modalClassName?: string;
  closeOnGoBack?: boolean;
  closeOnSave?: boolean;
  confirmBtnDisabled?: boolean;
  onDayChange?: (date: Date | Date[]) => void;
}

export const DatePickerWithPopup = <M extends Mode, V extends ValueType<M>>(props: DatePickerWithPopupProps<M, V>) => {
  const {
    mode = 'single',
    title = 'Select date',
    show: isPopupVisible,
    changeAfterInit = true,
    onClose,
    value,
    onChange,
    onBack,
    onSave,
    loading,
    needInit,
    containerStyle,
    maskClassName,
    maskStyle,
    modalClassName,
    closeOnGoBack,
    closeOnSave = true,
    confirmBtnDisabled,
    onDayChange,
    ...rest
  } = props;

  /** Controllable value */
  const [innerValue, setInnerValue] = useState<V>(() => {
    return (
      value ??
      ((mode === 'single'
        ? getExchangeFormattedDate()
        : mode === 'range'
        ? [getExchangeFormattedDate(), getExchangeFormattedDate()]
        : []) as V)
    );
  });
  const finalValue = value ?? innerValue;

  /** Edit value */
  const [editValue, setEditValue] = useState(finalValue);
  const [editMode, setEditMode] = useState<Mode>(mode);
  const handleChange = useLatestCallback((newDate: DatePickerProps['value']) => {
    if (Array.isArray(newDate)) {
      setEditValue(newDate.map((date) => getExchangeFormattedDate(date)) as V);
      onDayChange?.(newDate);
    } else {
      setEditValue(getExchangeFormattedDate(newDate) as V);
      onDayChange?.(newDate as Date);
    }
  });
  const { datePickerRef, success } = useEvaluationDatePickerContext();
  const [isInit, setIsInit] = useState(false);

  const handleClose = useLatestCallback(() => {
    setIsInit(false);
    onClose?.();
  });

  const handleSave = useLatestCallback(() => {
    setInnerValue(editValue);
    onSave?.(editValue);
    onChange?.(editValue);
    closeOnSave && handleClose();
  });

  /** Initialize */
  useEffect(() => {
    if (finalValue && isPopupVisible) {
      setEditValue(finalValue);
      setEditMode(mode);
    }
  }, [finalValue, mode, isPopupVisible]);

  useEffect(() => {
    if (!value && innerValue && changeAfterInit) {
      onChange?.(innerValue);
    }
  }, [value, innerValue, changeAfterInit]);

  const editDateValue = useMemo(() => {
    if (typeof editValue === 'string') {
      return dayjs(editValue as string, DATE_FORMAT_EXCHANGE).toDate();
    }
    return (editValue as string[]).map((date) => dayjs(date, DATE_FORMAT_EXCHANGE).toDate());
  }, [editValue]);

  const btnWording = editMode === 'single' ? 'Select date' : `Continue with ${editValue.length} selection(s)`;

  useEffect(() => {
    if (success && !isInit && isPopupVisible) {
      setIsInit(true);
    }
  }, [success, isInit, isPopupVisible]);

  return (
    <OperationSection
      className={cn('max-h-[90vh] web:mx-[var(--web-mx)] !pt-0 flex flex-col', modalClassName)}
      show={isPopupVisible}
      transitionDelay={0}
      clickMaskToClose
      onClose={handleClose}
      transition={DefaultTransitions.transitionY}
      mask
      maskClassName={maskClassName}
      maskStyle={maskStyle}
      containerStyle={containerStyle}
      goBack={closeOnGoBack ? handleClose : onBack}
      portal
      headerSticky
      title={title}
    >
      <Condition if={loading}>
        <SkeletonGroup rows={6} height="52px" />
      </Condition>
      <Condition if={isInit || !needInit}>
        <DatePicker
          ref={datePickerRef}
          className={cn(loading && 'hidden', 'h-[408px]')}
          value={editDateValue}
          mode={editMode}
          onDayChange={handleChange}
          {...rest}
        />
      </Condition>
      <div className="flex justify-center mt-[20px]">
        <Button
          disabled={loading || confirmBtnDisabled}
          className="btn-moe-large btn btn-primary w-full"
          onClick={handleSave}
        >
          {btnWording}
        </Button>
      </div>
    </OperationSection>
  );
};
