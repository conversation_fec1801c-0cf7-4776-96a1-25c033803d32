import { DatePicker } from '@moego/client-lib-components/dist/DatePicker/DatePicker';
import { useBoolean } from '@moego/client-lib-hooks/dist/useBoolean';
import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import dayjs from 'dayjs';
import { useDateFormatter } from 'hooks/useDateFormat';
import { useAtom } from 'jotai';
import { noop } from 'monofile-utilities/lib/consts';
import { memo, useMemo, useRef, useState } from 'react';
import { evaluationAvailableDatePayloadAtom } from 'state/boardingDaycare/evaluationAtom';
import { getExchangeFormattedDate, getMonthDates } from 'utils/date';
import { DatePickerWithPopup } from './DatePickerWithPopup';

export interface RenderInputParams {
  label?: string;
  errorMessage?: string;
  displayDateText: string;
  disabled?: boolean;
  onEdit: () => void;
}

export interface DatePickerPopupInputProps extends Omit<React.ComponentProps<typeof DatePicker>, 'value' | 'onChange'> {
  label?: string;
  containerStyle?: React.CSSProperties;
  maskClassName?: string;
  maskStyle?: React.CSSProperties;
  errorMessage?: string;
  required?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  onClose?: () => void;
  onBack?: () => void;
  loading?: boolean;
  disabled?: boolean;
  renderInput?: (params: RenderInputParams) => React.ReactNode;
  confirmBtnDisabled?: boolean;
}

export const DatePickerPopupInput = memo<DatePickerPopupInputProps>((props) => {
  const [isPopupVisible, setIsPopupVisible] = useBoolean(false);
  const {
    value,
    errorMessage,
    required,
    label,
    onChange,
    disabled,
    children,
    renderInput,
    onBack,
    onClose,
    confirmBtnDisabled,
    ...rest
  } = props;
  const format = useDateFormatter();

  /** Controllable value */
  const [innerValue, setInnerValue] = useState(value || getExchangeFormattedDate());
  const finalValue = value ?? innerValue;
  const displayDateText = useMemo(() => format(finalValue), [finalValue, format]);
  const [evaluationAvailablePayload, setEvaluationAvailablePayload] = useAtom(evaluationAvailableDatePayloadAtom);
  const originalMonthDates = useRef(getMonthDates());

  const handleChange = useLatestCallback((date: string) => {
    setInnerValue(date);
    onChange?.(date);
  });

  const renderFormInput = typeof children === 'function' ? children : renderInput;
  const pureChildren = typeof children !== 'function' ? children : undefined;

  const handleClosePopup = () => {
    setIsPopupVisible(false);
    if (evaluationAvailablePayload) {
      if (!dayjs(evaluationAvailablePayload.startDate).isSame(originalMonthDates.current.startDate, 'month')) {
        setEvaluationAvailablePayload((pre) => ({
          ...pre,
          ...originalMonthDates.current,
        }));
      }
    }
  };

  return (
    <div>
      {renderFormInput &&
        renderFormInput({
          label,
          errorMessage,
          displayDateText,
          disabled,
          onEdit: disabled ? noop : () => setIsPopupVisible(true),
        })}
      {pureChildren}
      <DatePickerWithPopup
        {...rest}
        show={isPopupVisible}
        onClose={() => {
          handleClosePopup();
          onClose?.();
        }}
        onBack={() => {
          handleClosePopup();
          onBack?.();
        }}
        onSave={() => {
          handleClosePopup();
        }}
        closeOnSave={false}
        mode="single"
        value={finalValue}
        onChange={handleChange}
        needInit
        confirmBtnDisabled={confirmBtnDisabled}
      />
    </div>
  );
});
