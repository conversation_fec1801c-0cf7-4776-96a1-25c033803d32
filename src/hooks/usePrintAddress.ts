import { formatAddress } from '@moego/client-lib-utils/dist/format';
import { useAtomValue } from 'jotai';
import { useCallback, useMemo } from 'react';
import { businessLandingConfigState, businessPreferenceState } from 'state/business/state';
import { AddressDetail } from 'types/entity';

export function usePrintAddress() {
  return useCallback((address: AddressDetail): string => {
    if (!address) return '';

    return formatAddress(address);
  }, []);
}

// Print 与 Printed的区别是，一个是方法调，一个你直接渲染用。知识点

export function usePrintedBusinessAddress() {
  const { data } = useAtomValue(businessPreferenceState) || {};
  const print = usePrintAddress();

  return useMemo(
    () =>
      print({
        address1: data?.address1,
        address2: data?.address2,
        state: data?.addressState,
        zipcode: data?.addressZipcode,
        country: data?.addressCountry,
        city: data?.addressCity,
      }),
    [data],
  );
}

// 跟上面的是一样的逻辑, 但是这个是给landing page用的，保持数据的隔离
export function useLandingPagePrintedAddress() {
  const { address } = useAtomValue(businessLandingConfigState).data || {};
  const print = usePrintAddress();

  return useMemo(() => print(address?.addressDetails!), [address]);
}
