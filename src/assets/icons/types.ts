// This file is generated automatically. Do not edit it manually.

export enum IconType {
  agreement = 'agreement',
  animalChecklistSquare = 'animal-checklist-square',
  animalDogHouse = 'animal-dog-house',
  animalPrintMedal = 'animal-print-medal',
  applePay = 'apple-pay',
  arrowDown = 'arrow-down',
  arrowLeftRounded = 'arrow-left-rounded',
  arrowLeft = 'arrow-left',
  arrowLocationDirection = 'arrow-location-direction',
  arrowRightRounded = 'arrow-right-rounded',
  arrowRight = 'arrow-right',
  bagShopping = 'bag-shopping',
  ball = 'ball',
  calendar = 'calendar',
  call = 'call',
  camera = 'camera',
  carParkingPin = 'car-parking-pin',
  catClaws = 'cat-claws',
  centralBarkEmpty = 'central-bark-empty',
  checkCircleFillWithStar = 'check-circle-fill-with-star',
  checkCircleFill = 'check-circle-fill',
  checked = 'checked',
  checkmarkAvailable = 'checkmark-available',
  checkmarkUnavailable = 'checkmark-unavailable',
  close = 'close',
  cofRequestLock = 'cof-request-lock',
  copy = 'copy',
  creditCards = 'credit-cards',
  delete = 'delete',
  directionH = 'direction-h',
  discount = 'discount',
  divider = 'divider',
  documentsFile = 'documents-file',
  dogSleep = 'dog-sleep',
  dollarFilled = 'dollar-filled',
  doneCheck = 'done-check',
  edit = 'edit',
  email = 'email',
  evaluation = 'evaluation',
  exit = 'exit',
  genderFemale = 'gender-female',
  genderMale = 'gender-male',
  googlePay = 'google-pay',
  google = 'google',
  groomerAvatar = 'groomer-avatar',
  info = 'info',
  instagram = 'instagram',
  k9Empty = 'k9-empty',
  landingBooknow = 'landing-booknow',
  leftFootprintBg = 'left-footprint-bg',
  left = 'left',
  localBiz = 'local-biz',
  locationMark = 'location-mark',
  locationNavigateOutline = 'location-navigate-outline',
  locationNavigate = 'location-navigate',
  locationPin = 'location-pin',
  lock = 'lock',
  logo = 'logo',
  majorAddOnOutlined = 'major-add-on-outlined',
  majorBoardingOutlined = 'major-boarding-outlined',
  majorCalendarOutlined = 'major-calendar-outlined',
  majorCardOutlined = 'major-card-outlined',
  majorChevronRightOutlined = 'major-chevron-right-outlined',
  majorCloseOutlined = 'major-close-outlined',
  majorDaycareOutlined = 'major-daycare-outlined',
  majorDogWalkingOutlined = 'major-dog-walking-outlined',
  majorEvaluationOutlined = 'major-evaluation-outlined',
  majorFileOutlined = 'major-file-outlined',
  majorGroomingOutlined = 'major-grooming-outlined',
  majorMembershipOutlined = 'major-membership-outlined',
  majorPackageOutlined = 'major-package-outlined',
  majorPawsOutlined = 'major-paws-outlined',
  majorPlusOutlined = 'major-plus-outlined',
  majorTaskOutlined = 'major-task-outlined',
  majorTourOutline = 'major-tour-outline',
  medicalSyringeAddPlus = 'medical-syringe-add-plus',
  membershipBadge = 'membership-badge',
  message = 'message',
  minorEditFilled = 'minor-edit-filled',
  minorEditOutlined = 'minor-edit-outlined',
  minorInfoFilled = 'minor-info-filled',
  minorMinusOutlined = 'minor-minus-outlined',
  minorPlusOutlined = 'minor-plus-outlined',
  minorRightArrowOutlined = 'minor-right-arrow-outlined',
  minorTrashOutlined = 'minor-trash-outlined',
  minorUserFilled = 'minor-user-filled',
  minus = 'minus',
  packagePurchaseBg2Mobile = 'package-purchase-bg2-mobile',
  packagePurchaseBg2Web = 'package-purchase-bg2-web',
  petCatAvatar = 'pet-cat-avatar',
  petDogAvatar = 'pet-dog-avatar',
  petGeneralAvatar = 'pet-general-avatar',
  plus = 'plus',
  polygonWarn = 'polygon-warn',
  question = 'question',
  quote = 'quote',
  rightFootprintBg = 'right-footprint-bg',
  right = 'right',
  roundedGreen = 'rounded-green',
  search = 'search',
  sendShare = 'send-share',
  serviceArea = 'service-area',
  share = 'share',
  socialFacebook = 'social-facebook',
  socialIg = 'social-ig',
  socialYelp = 'social-yelp',
  spin = 'spin',
  star = 'star',
  syringe = 'syringe',
  tiktok = 'tiktok',
  timeClock = 'time-clock',
  typeHybrid = 'type-hybrid',
  typeMobile = 'type-mobile',
  typeSalon = 'type-salon',
  unChecked = 'un-checked',
  uncheckedCircleGrey = 'unchecked-circle-grey',
  uploadFile = 'upload-file',
  userProfileRefresh = 'user-profile-refresh',
  warning = 'warning',
  watchClock = 'watch-clock',
}
