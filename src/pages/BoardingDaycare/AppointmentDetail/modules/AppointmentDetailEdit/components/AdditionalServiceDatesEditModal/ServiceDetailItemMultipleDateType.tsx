import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useInput } from '@moego/client-lib-widgets/dist/Input/utils';
import { Radio } from 'components/Layout/Radio/Radio';
import { RadioGroup } from 'components/Layout/Radio/RadioGroup';
import { useShortWeekdayAndDateFormatter } from 'hooks/useDateFormat';
import { CounterInput } from 'pages/BoardingDaycare/ChooseService/CounterInput';
import React, { useMemo } from 'react';
import { cn } from 'utils/classNames';
import { createEnum } from 'utils/enum';
import { Condition } from 'widgets/Condition';
import { useAppointmentDetailEdit } from '../../AppointmentDetailEdit.context';
import { useAdditionalServiceDatePickerModal } from './AdditionalServiceDatePickerModal.context';
import { PetServiceDetailItemInfo } from './types';

// 这个组件目前只有 boarding 会使用，万一要扩展 daycare 编辑，对应改一下
const MainCareTypeIsBoarding = true;

const DefaultQuantityPerDay = 1;

export interface ServiceDetailItemMultipleDateTypeProps {
  serviceDetailInfo: PetServiceDetailItemInfo;
}

export const ServiceDetailItemMultipleDateType: React.FC<ServiceDetailItemMultipleDateTypeProps> = (props) => {
  const { serviceDetailInfo } = props;
  const { serviceDetailKey } = serviceDetailInfo;

  // store related
  const { additionalServiceDetailMap = {}, updateAdditionalServiceDetail } = useAppointmentDetailEdit();
  const currentAdditionalServiceDetail = additionalServiceDetailMap[serviceDetailKey];
  const {
    dateType: currentDateType,
    careType,
    specificDates,
    quantityPerDay,
    serviceName,
  } = useMemo(
    () => ({
      ...serviceDetailInfo,
      ...currentAdditionalServiceDetail,
    }),
    [currentAdditionalServiceDetail, serviceDetailInfo],
  );

  const { openDatePicker, closeDatePicker } = useAdditionalServiceDatePickerModal();

  const handleDateTypeChange = (newDateType: PetDetailDateType) => {
    if (newDateType !== PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE) {
      updateAdditionalServiceDetail(serviceDetailKey, {
        dateType: newDateType,
        specificDates: [],
      });
    } else {
      updateAdditionalServiceDetail(serviceDetailKey, {
        dateType: newDateType,
      });
    }
  };

  const handleSpecificDatesEdit = () => {
    openDatePicker({
      isSingleMode: false,
      valueMultiple: specificDates,
      onChangeMultiple: (value) => {
        updateAdditionalServiceDetail(serviceDetailKey, {
          dateType: PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE,
          specificDates: value,
        });
        closeDatePicker();
      },
    });
  };

  const handleQuantityPerDayUpdate = (value: string) => {
    updateAdditionalServiceDetail(serviceDetailKey, {
      quantityPerDay: +value,
    });
  };

  const formatDate = useShortWeekdayAndDateFormatter();

  // boarding multiple mode
  const addonLabels = getLegacyDateTypeAddonLabels(MainCareTypeIsBoarding);
  const newPetCountInput = useInput(`${quantityPerDay || DefaultQuantityPerDay}`);

  return (
    <div className="rounded-[20px] border border-[#E9ECEF] bg-white overflow-hidden">
      <div className="px-[20px] py-[16px] text-[18px] leading-[24px] font-semibold">{serviceName}</div>
      <div className="bg-[#fafafa] px-[20px] py-[16px]">
        <RadioGroup
          className="flex flex-col gap-y-[16px]"
          value={currentDateType}
          onChange={(e) => {
            if (+e.target.value === PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE && !specificDates?.length) {
              handleSpecificDatesEdit();
              return;
            }
            handleDateTypeChange(+e.target.value as PetDetailDateType);
          }}
        >
          {addonLabels.map(({ dateType }) => {
            const showFooterDate =
              LegacyDateTypeEnum.mapLabels[dateType].specificDates &&
              specificDates?.length &&
              currentDateType === dateType;
            const showFooterQuantity = currentDateType === dateType && careType !== ServiceItemType.DAYCARE;
            const showRadioFooter = showFooterDate || showFooterQuantity;
            return (
              <Radio
                id={`date-type-${serviceDetailKey}-${dateType}`}
                key={dateType}
                value={dateType}
                labelClassName="flex-1"
                footer={
                  showRadioFooter && (
                    <>
                      <Condition if={showFooterDate}>
                        <div className="mt-[-12px] text-[14px] leading-[18px] flex flex-col gap-[4px]">
                          {specificDates?.map((date) => (
                            <div key={date}>{formatDate(date)}</div>
                          ))}
                        </div>
                      </Condition>
                      <Condition if={showFooterQuantity}>
                        <div
                          className={cn('flex items-center  ml-[12px] mt-[4px]', {
                            'mt-[-8px]': !showFooterDate,
                          })}
                        >
                          <CounterInput
                            className="!ml-[-12px] !mr-[4px]"
                            input={newPetCountInput}
                            required
                            minValue={1}
                            maxValue={9}
                            onValueChange={handleQuantityPerDayUpdate}
                          />
                          <div className="text-[#888C96] text-[14px]">per day</div>
                        </div>
                      </Condition>
                    </>
                  )
                }
              >
                <div className="flex justify-between">
                  <div className="text-[#202020] text-[16px] leading-[22px] font-[500]">
                    {LegacyDateTypeEnum.mapLabels[dateType]?.getLabel(MainCareTypeIsBoarding)}
                  </div>
                  <Condition
                    if={
                      currentDateType === dateType &&
                      LegacyDateTypeEnum.mapLabels[dateType].specificDates &&
                      specificDates?.length
                    }
                  >
                    <div className="text-primary cursor-pointer text-[14px]" onClick={handleSpecificDatesEdit}>
                      Edit
                    </div>
                  </Condition>
                </div>
              </Radio>
            );
          })}
        </RadioGroup>
      </div>
    </div>
  );
};

const getLegacyDateTypeAddonLabels = (isBoarding?: boolean) =>
  Object.values(LegacyDateTypeEnum.mapLabels)
    .filter(
      ({ dateType }) => dateType !== PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY || isBoarding,
    )
    .sort((pre, next) => pre.order - next.order);

const LegacyDateTypeEnum = createEnum({
  Everyday: [
    PetDetailDateType.PET_DETAIL_DATE_EVERYDAY,
    {
      getLabel: (isBoarding?: boolean) => {
        return isBoarding ? 'Every day except for checkout day' : 'Every day';
      },
      specificDates: false,
      dateType: PetDetailDateType.PET_DETAIL_DATE_EVERYDAY,
      order: 1,
    },
  ],
  EverydayIncludingCheckout: [
    PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY,
    {
      getLabel: () => 'Every day including checkout day',
      specificDates: false,
      dateType: PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY,
      order: 2,
    },
  ],
  CertainDates: [
    PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE,
    {
      getLabel: () => 'Certain date(s)',
      specificDates: true,
      dateType: PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE,
      order: 3,
    },
  ],
});
