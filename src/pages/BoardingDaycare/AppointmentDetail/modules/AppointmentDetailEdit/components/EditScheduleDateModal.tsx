import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { DatePicker } from '@moego/client-lib-components/dist/DatePicker/DatePicker';
import { useLatestCallback } from '@moego/client-lib-hooks/dist/useLatestCallback';
import { OperationSection } from 'components/Layout/OperationSection';
import { useChooseSchedule } from 'pages/BoardingDaycare/ChooseSchedule/hooks/useChooseSchedule';
import React, { useEffect } from 'react';
import { cn } from 'utils/classNames';
import { Button } from 'widgets/Button/Button';
import { Condition } from 'widgets/Condition';
import { SkeletonGroup } from 'widgets/Skeleton';
import { DefaultTransitions } from 'widgets/Transition/defaults';
import { useAppointmentDetail } from '../../../hooks/useAppointmentDetail';
import { useAppointmentDetailEdit } from '../AppointmentDetailEdit.context';
import { maskClassName } from '../AppointmentDetailEditModals.utils';

export interface EditScheduleDateModalProps {
  show?: boolean;
  onClose?: () => void;
  onNext?: () => Promise<void> | void;
}

export const EditScheduleDateModal: React.FC<EditScheduleDateModalProps> = (props) => {
  const { show, onClose, onNext } = props;
  const { scheduleDate, updateScheduleDate } = useAppointmentDetailEdit();
  const appointmentDetail = useAppointmentDetail();
  const serviceItemType = appointmentDetail?.appointment?.mainCareType || ServiceItemType.BOARDING;

  const { isValidDateValue, nextButtonTitle, dateState, setDateState, datePickerProps } = useChooseSchedule({
    serviceItemType,
    daycareUseSingleDate: true,
    defaultScheduleDate: {
      petKey: [],
      bookingDateList: [],
      ...scheduleDate,
    },
  });

  const handleInitDateState = useLatestCallback(() => {
    setDateState({
      petKey: [],
      bookingDateList: [],
      ...scheduleDate,
    });
  });

  useEffect(() => {
    if (show) {
      handleInitDateState();
    }
  }, [show]);

  const handleConfirm = useLatestCallback(async () => {
    updateScheduleDate(dateState);
    /**
     * 由于 Context 之上直接用 useState 存的状态，需要经历一次渲染，才能拿到最新的 schedule date
     * 目前没封 sleep 方法，但这里更多不是想 sleep，而是要在下一次 render 后再去取，恰好 setTimeout 推迟到下一次 Macrotask 的时候已渲染一次，刷过一轮最新数据
     */
    await new Promise((r) => setTimeout(r, 0));
    await onNext?.();
  });

  return (
    <OperationSection
      className="max-h-[90vh] web:mx-[var(--web-mx)] !pt-0 flex flex-col"
      titleClassName="flex-1"
      show={show}
      onClose={onClose}
      transitionDelay={0}
      clickMaskToClose
      transition={DefaultTransitions.transitionY}
      mask
      maskClassName={maskClassName}
      portal
      headerSticky
      headerClassName="flex justify-center"
      title="Edit dates"
    >
      <div className="h-[352px] text-start">
        <Condition if={datePickerProps.availableDateLoading}>
          <SkeletonGroup rows={6} height="52px" />
        </Condition>
        <DatePicker
          className={cn({
            // 避免组件销毁时重置内部状态，使用 hidden 而不是条件渲染
            hidden: datePickerProps.availableDateLoading,
          })}
          disableRangeReorder
          mode={datePickerProps.mode}
          value={datePickerProps.value as Date[]}
          ref={datePickerProps.datePickerRef}
          onDayChange={datePickerProps.onDayChange}
          onMonthChange={datePickerProps.onMonthChange}
          disabledDate={datePickerProps.disabledDate}
        />
      </div>
      <div className="flex justify-center mt-[20px]">
        <Button className="btn-moe-large btn btn-primary w-full" onClick={handleConfirm} disabled={!isValidDateValue}>
          {nextButtonTitle}
        </Button>
      </div>
    </OperationSection>
  );
};
