import { FC, memo, ReactNode } from 'react';
import { cn } from 'utils/classNames';
interface AppointmentInfoBoxProps {
  children: ReactNode;
  className?: string;
}
export const AppointmentInfoBox: FC<AppointmentInfoBoxProps> = memo(({ className, children }) => {
  return <div className={cn('flex flex-col gap-[4px]', className)}>{children}</div>;
});

enum AppointmentTextSize {
  Large = 'large',
  Medium = 'medium',
  Small = 'small',
  Mini = 'mini',
}

interface AppointmentTextProps {
  text: ReactNode;
  bold?: boolean;
  className?: string;
  size?: `${AppointmentTextSize}`;
}
export const AppointmentText: FC<AppointmentTextProps> = memo((props) => {
  const { text, bold, size, className } = props;
  return (
    <div
      className={cn(
        'text-[14px] text-[#888C96]',
        {
          'font-bold': bold,
          'text-[12px]': size === AppointmentTextSize.Mini,
          'text-[14px]': size === AppointmentTextSize.Small,
          'text-[16px] text-[#333]': size === AppointmentTextSize.Medium,
          'text-[18px] text-[#333]': size === AppointmentTextSize.Large,
        },
        className,
      )}
    >
      {text}
    </div>
  );
});
